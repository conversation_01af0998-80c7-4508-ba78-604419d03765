// utils/api.js
// 支持多种网络环境的API配置
const getBaseUrl = () => {
  // 可以根据不同环境返回不同的URL
  const urls = [
    'http://localhost:3000',
    'http://127.0.0.1:3000',
    'http://************:3000' // 您的数据库服务器IP
  ];

  // 在开发环境中，可以尝试不同的URL
  return urls[0]; // 默认使用localhost
};

const baseUrl = getBaseUrl();

// 测试网络连接
const testConnection = () => {
  return new Promise((resolve, reject) => {
    wx.request({
      url: baseUrl + '/health',
      method: 'GET',
      timeout: 5000,
      success: (res) => {
        console.log('网络连接测试成功:', res);
        resolve(true);
      },
      fail: (err) => {
        console.error('网络连接测试失败:', err);
        reject(err);
      }
    });
  });
};

// 统一的错误提示函数
const showErrorToast = (message = '操作失败，请稍后重试') => {
  wx.showToast({
    title: message,
    icon: 'none',
    duration: 2000
  });
};

const request = (url, method, data) => {
  wx.showLoading({
    title: '加载中...',
    mask: true
  });
  return new Promise((resolve, reject) => {
    wx.request({
      url: baseUrl + url,
      method: method,
      data: data,
      success: (res) => {
        wx.hideLoading();
        wx.hideLoading();
        // 检查 HTTP 状态码是否在 2xx 范围内
        if (res.statusCode >= 200 && res.statusCode < 300) {
          // 检查响应体是否存在以及是否包含业务状态码 code
          if (res.data && typeof res.data.code !== 'undefined') {
            // 如果存在 code 字段，根据 code 判断业务成功或失败
            if (res.data.code === 0) { // 假设 code 0 表示业务成功
              resolve(res.data);
            } else {
              // 业务错误（code 非 0）
              const errorMessage = res.data.message ? res.data.message : '业务处理失败';
              showErrorToast(errorMessage);
              reject(res.data);
            }
          } else {
            // 如果没有 code 字段，但 HTTP 状态码是 2xx，也视为业务成功
            resolve(res.data);
          }
        } else {
          // HTTP 状态码非 2xx
          const errorMessage = res.data && res.data.message ? res.data.message : `请求失败: ${res.statusCode}`;
          showErrorToast(errorMessage);
          reject(res);
        }
      },
      fail: (err) => {
        wx.hideLoading();
        showErrorToast('网络请求失败，请稍后重试');
        reject(err);
      }
    });
  });
};

const submitPatientInfo = (data) => {
  return request('/api/patient/info', 'POST', data);
};

const uploadImage = (filePath, imageType = '其他', patientId = null, onProgressUpdate) => {
  console.log('=== 开始上传图片 ===');
  console.log('文件路径:', filePath);
  console.log('图片类型:', imageType);
  console.log('患者ID:', patientId);

  return new Promise((resolve, reject) => {
    // 构建表单数据
    const formData = {
      image_type: imageType
    };

    // 如果有患者ID，添加到表单数据中
    if (patientId) {
      formData.patient_id = patientId;
    }

    console.log('表单数据:', formData);
    console.log('上传URL:', baseUrl + '/api/image/upload');

    const uploadTask = wx.uploadFile({
      url: baseUrl + '/api/image/upload',
      filePath: filePath,
      name: 'images', // 文件字段名
      formData: formData,
      timeout: 30000, // 30秒超时
      success: (res) => {
        console.log('上传请求成功回调');
        console.log('状态码:', res.statusCode);
        console.log('响应数据:', res.data);

        try {
          const data = JSON.parse(res.data);
          if (res.statusCode >= 200 && res.statusCode < 300) {
            console.log('上传成功，解析后的数据:', data);
            resolve(data);
          } else {
            console.error('HTTP状态码错误:', res.statusCode);
            const errorMessage = data && data.message ? data.message : `上传失败: ${res.statusCode}`;
            showErrorToast(errorMessage);
            reject(data);
          }
        } catch (e) {
          console.error('JSON解析失败:', e);
          console.error('原始响应数据:', res.data);
          showErrorToast('上传响应解析失败');
          reject(e);
        }
      },
      fail: (err) => {
        console.error('上传请求失败:', err);
        showErrorToast('图片上传失败，请稍后重试');
        reject(err);
      }
    });

    // 监听上传进度变化
    if (onProgressUpdate && typeof onProgressUpdate === 'function') {
      uploadTask.onProgressUpdate((res) => {
        onProgressUpdate(res.progress); // 传递进度百分比
      });
    }
  });
};

module.exports = {
  request,
  submitPatientInfo,
  uploadImage,
  showErrorToast, // 导出错误提示函数，方便页面直接调用
  testConnection,
  baseUrl
};

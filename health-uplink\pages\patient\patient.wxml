<!--pages/patient/patient.wxml-->
<page-meta>
  <navigation-bar title="患者信息" showBack="{{true}}" showHome="{{false}}"></navigation-bar>
</page-meta>
<view class="container" style="padding-top: {{contentPaddingTop}}px;" bindtouchstart="onTouchStart" bindtouchmove="onTouchMove" bindtouchend="onTouchEnd">
  <view class="guide-text">
    请准确填写以下患者信息，以便医生更好地了解您的病情。
  </view>
  <form>
    <view class="form-item">
      <text class="label">姓名:</text>
      <input type="text" data-field="name" value="{{patientInfo.name}}" bindinput="onInput" placeholder="请输入姓名" class="{{errors.name ? 'input-error' : ''}}" />
      <text wx:if="{{errors.name}}" class="error-message">{{errors.name}}</text>
    </view>
    <view class="form-item">
      <text class="label">年龄:</text>
      <input type="number" data-field="age" value="{{patientInfo.age}}" bindinput="onInput" placeholder="请输入年龄" class="{{errors.age ? 'input-error' : ''}}" />
      <text wx:if="{{errors.age}}" class="error-message">{{errors.age}}</text>
    </view>
    <view class="form-item">
      <text class="label">病史:</text>
      <textarea data-field="medical_history" value="{{patientInfo.medical_history}}" bindinput="onInput" placeholder="请输入病史" auto-height />
    </view>
    <view class="form-item">
      <text class="label">症状:</text>
      <textarea data-field="symptoms" value="{{patientInfo.symptoms}}" bindinput="onInput" placeholder="请输入症状" auto-height />
    </view>
    <button class="submit-button" bindtap="submitForm" disabled="{{isLoading}}">提交</button>
  </form>
</view>

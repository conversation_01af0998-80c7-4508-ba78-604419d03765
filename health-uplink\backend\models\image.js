const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/config');

const Image = sequelize.define('Image', {
  image_id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true,
    allowNull: false
  },
  filename: {
    type: DataTypes.STRING(255),
    allowNull: true // 兼容旧数据
  },
  original_name: {
    type: DataTypes.STRING(255),
    allowNull: true
  },
  file_path: {
    type: DataTypes.STRING(500),
    allowNull: true // 兼容旧数据
  },
  file_size: {
    type: DataTypes.INTEGER,
    allowNull: true
  },
  mime_type: {
    type: DataTypes.STRING(100),
    allowNull: true
  },
  patient_id: {
    type: DataTypes.INTEGER,
    allowNull: true // 允许为空，支持独立图片
  },
  image_url: {
    type: DataTypes.STRING(255),
    allowNull: false // 保持向后兼容
  },
  image_type: {
    type: DataTypes.STRING(50), // 例如：'检查单', '患处照片'
    allowNull: true
  },
  created_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW,
    allowNull: false
  }
}, {
  tableName: 'images',
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: false // 图片模型不需要 updated_at 字段
});

module.exports = Image;
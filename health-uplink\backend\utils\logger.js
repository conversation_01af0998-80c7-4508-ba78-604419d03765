const winston = require('winston');
const path = require('path');

const logDir = path.join(__dirname, '../../log/backend'); // 日志文件存储目录

// 创建日志目录（如果不存在）
const fs = require('fs');
if (!fs.existsSync(logDir)) {
  fs.mkdirSync(logDir);
}

// 定义日志格式
const logFormat = winston.format.combine(
  winston.format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss' }),
  winston.format.printf(info => `${info.timestamp} ${info.level}: ${info.message}`)
);

// 创建 logger 实例
const logger = winston.createLogger({
  level: process.env.NODE_ENV === 'development' ? 'debug' : 'info', // 开发环境记录 debug 级别及以上，生产环境记录 info 级别及以上
  format: logFormat,
  transports: [
    // 控制台输出
    new winston.transports.Console({
      format: winston.format.combine(
        winston.format.colorize(), // 控制台输出带颜色
        logFormat
      )
    }),
    // 文件输出（所有级别）
    new winston.transports.File({
      filename: path.join(logDir, 'combined.log'),
      level: 'debug' // 记录 debug 级别及以上到文件
    }),
    // 文件输出（错误级别）
    new winston.transports.File({
      filename: path.join(logDir, 'error.log'),
      level: 'error' // 记录 error 级别及以上到文件
    })
  ]
});

// 创建一个 HTTP 请求日志中间件
logger.stream = {
  write: function(message, encoding) {
    logger.info(message.trim()); // 记录 HTTP 请求信息
  },
};

module.exports = logger;
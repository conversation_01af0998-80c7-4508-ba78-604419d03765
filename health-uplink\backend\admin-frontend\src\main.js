import { createApp } from 'vue'
import './style.css'
import './styles/design-system.css' // 引入设计系统样式
import App from './App.vue'

import router from './router' // 引入路由实例

import ElementPlus from 'element-plus' // 引入 Element Plus
import 'element-plus/dist/index.css' // 引入 Element Plus 样式
import * as ElementPlusIconsVue from '@element-plus/icons-vue' // 引入 Element Plus 图标

const app = createApp(App)

// 注册所有图标
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  app.component(key, component)
}

app.use(router).use(ElementPlus).mount('#app') // 将路由和 Element Plus 挂载到应用上

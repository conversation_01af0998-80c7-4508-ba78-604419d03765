// components/navigation-bar/navigation-bar.js
Component({
  /**
   * 组件的属性列表
   */
  properties: {
    title: {
      type: String,
      value: ''
    },
    showBack: {
      type: Boolean,
      value: true // 默认显示返回按钮
    },
    showHome: {
      type: Boolean,
      value: false // 默认不显示返回首页按钮
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    statusBarHeight: 0,
    navigationBarHeight: 44
  },

  /**
   * 组件的方法列表
   */
  methods: {
    back() {
      if (getCurrentPages().length > 1) {
        wx.navigateBack();
      } else {
        wx.reLaunch({
          url: '/pages/index/index'
        });
      }
    },
    home() {
      wx.reLaunch({
        url: '/pages/index/index'
      });
    }
  },
  lifetimes: {
    attached() {
      const { statusBarHeight } = wx.getSystemInfoSync();
      this.setData({
        statusBarHeight,
        navigationBarHeight: statusBarHeight + 44 // 44px is the default navigation bar height
      });
    }
  }
})

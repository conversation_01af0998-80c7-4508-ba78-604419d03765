<template>
  <ContentContainer 
    title="UI 组件展示"
    description="Health Uplink 标准组件库展示和使用示例"
    :show-header="true"
  >
    <!-- 设计系统展示 -->
    <div class="demo-section">
      <h3 class="section-title">设计系统</h3>
      
      <!-- 颜色系统 -->
      <div class="subsection">
        <h4 class="subsection-title">颜色系统</h4>
        <div class="color-palette">
          <div class="color-group">
            <h5>主色调</h5>
            <div class="color-items">
              <div class="color-item">
                <div class="color-block" style="background: var(--primary-color)"></div>
                <span>Primary</span>
              </div>
              <div class="color-item">
                <div class="color-block" style="background: var(--primary-light)"></div>
                <span>Primary Light</span>
              </div>
              <div class="color-item">
                <div class="color-block" style="background: var(--primary-dark)"></div>
                <span>Primary Dark</span>
              </div>
            </div>
          </div>
          
          <div class="color-group">
            <h5>辅助色</h5>
            <div class="color-items">
              <div class="color-item">
                <div class="color-block" style="background: var(--success-color)"></div>
                <span>Success</span>
              </div>
              <div class="color-item">
                <div class="color-block" style="background: var(--warning-color)"></div>
                <span>Warning</span>
              </div>
              <div class="color-item">
                <div class="color-block" style="background: var(--error-color)"></div>
                <span>Error</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 字体系统 -->
      <div class="subsection">
        <h4 class="subsection-title">字体系统</h4>
        <div class="typography-demo">
          <div class="text-xxl">主标题 (24px)</div>
          <div class="text-xl">标题 (20px)</div>
          <div class="text-lg">小标题 (18px)</div>
          <div class="text-md">正文 (16px)</div>
          <div class="text-sm">正文（小）(14px)</div>
          <div class="text-xs">辅助文字 (12px)</div>
        </div>
      </div>

      <!-- 间距系统 -->
      <div class="subsection">
        <h4 class="subsection-title">间距系统</h4>
        <div class="spacing-demo">
          <div class="spacing-item">
            <div class="spacing-block" style="width: 4px;"></div>
            <span>XS (4px)</span>
          </div>
          <div class="spacing-item">
            <div class="spacing-block" style="width: 8px;"></div>
            <span>SM (8px)</span>
          </div>
          <div class="spacing-item">
            <div class="spacing-block" style="width: 16px;"></div>
            <span>MD (16px)</span>
          </div>
          <div class="spacing-item">
            <div class="spacing-block" style="width: 24px;"></div>
            <span>LG (24px)</span>
          </div>
          <div class="spacing-item">
            <div class="spacing-block" style="width: 32px;"></div>
            <span>XL (32px)</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 组件展示 -->
    <div class="demo-section">
      <h3 class="section-title">标准组件</h3>
      
      <!-- 搜索表单组件 -->
      <div class="subsection">
        <h4 class="subsection-title">SearchForm - 搜索表单</h4>
        <SearchForm
          :search-fields="demoSearchFields"
          @search="handleDemoSearch"
          @reset="handleDemoReset"
        />
      </div>

      <!-- 数据表格组件 -->
      <div class="subsection">
        <h4 class="subsection-title">DataTable - 数据表格</h4>
        <DataTable
          :data="demoTableData"
          :columns="demoTableColumns"
          :pagination="demoPagination"
          title="示例数据表格"
          @refresh="handleRefresh"
        >
          <template #actions="{ row }">
            <el-button type="text" size="small">查看</el-button>
            <el-button type="text" size="small">编辑</el-button>
            <el-button type="text" size="small" class="text-error">删除</el-button>
          </template>
        </DataTable>
      </div>

      <!-- 分页组件 -->
      <div class="subsection">
        <h4 class="subsection-title">StandardPagination - 分页组件</h4>
        <StandardPagination
          :current-page="demoPagination.page"
          :page-size="demoPagination.pageSize"
          :total="demoPagination.total"
          :show-info="true"
        />
      </div>

      <!-- 表单组件 -->
      <div class="subsection">
        <h4 class="subsection-title">StandardForm - 标准表单</h4>
        <StandardForm
          :form-fields="demoFormFields"
          :form-model="demoFormModel"
          :show-actions="true"
          @submit="handleFormSubmit"
        />
      </div>
    </div>

    <!-- 工具类展示 -->
    <div class="demo-section">
      <h3 class="section-title">工具类展示</h3>
      
      <div class="subsection">
        <h4 class="subsection-title">文字样式</h4>
        <div class="utility-demo">
          <div class="text-primary font-bold">主要文字 - 粗体</div>
          <div class="text-secondary font-medium">次要文字 - 中等</div>
          <div class="text-tertiary font-normal">第三级文字 - 正常</div>
          <div class="text-success">成功状态文字</div>
          <div class="text-warning">警告状态文字</div>
          <div class="text-error">错误状态文字</div>
        </div>
      </div>

      <div class="subsection">
        <h4 class="subsection-title">布局工具</h4>
        <div class="utility-demo">
          <div class="flex items-center justify-between p-md bg-tertiary rounded-md mb-sm">
            <span>左侧内容</span>
            <span>右侧内容</span>
          </div>
          <div class="flex flex-col gap-sm">
            <div class="p-sm bg-primary rounded-sm shadow-sm">卡片 1</div>
            <div class="p-sm bg-primary rounded-sm shadow-sm">卡片 2</div>
            <div class="p-sm bg-primary rounded-sm shadow-sm">卡片 3</div>
          </div>
        </div>
      </div>
    </div>
  </ContentContainer>
</template>

<script>
import { defineComponent, ref, reactive } from 'vue'
import ContentContainer from '../components/ContentContainer.vue'
import SearchForm from '../components/SearchForm.vue'
import DataTable from '../components/DataTable.vue'
import StandardPagination from '../components/StandardPagination.vue'
import StandardForm from '../components/StandardForm.vue'

export default defineComponent({
  name: 'ComponentDemo',
  components: {
    ContentContainer,
    SearchForm,
    DataTable,
    StandardPagination,
    StandardForm
  },
  setup() {
    // 搜索表单演示数据
    const demoSearchFields = ref([
      {
        key: 'name',
        label: '姓名',
        type: 'input',
        placeholder: '请输入姓名'
      },
      {
        key: 'status',
        label: '状态',
        type: 'select',
        placeholder: '请选择状态',
        options: [
          { label: '启用', value: 1 },
          { label: '禁用', value: 0 }
        ]
      },
      {
        key: 'date_range',
        label: '日期范围',
        type: 'daterange',
        placeholder: '选择日期范围'
      }
    ])

    // 表格演示数据
    const demoTableData = ref([
      { id: 1, name: '张三', age: 25, status: 1, email: '<EMAIL>', created_at: '2024-01-01' },
      { id: 2, name: '李四', age: 30, status: 0, email: '<EMAIL>', created_at: '2024-01-02' },
      { id: 3, name: '王五', age: 28, status: 1, email: '<EMAIL>', created_at: '2024-01-03' }
    ])

    const demoTableColumns = ref([
      { prop: 'id', label: 'ID', width: 80, align: 'center' },
      { prop: 'name', label: '姓名', minWidth: 100 },
      { prop: 'age', label: '年龄', width: 80, align: 'center' },
      { prop: 'email', label: '邮箱', minWidth: 150 },
      {
        prop: 'status',
        label: '状态',
        width: 100,
        type: 'status',
        statusMap: {
          1: { text: '启用', type: 'success' },
          0: { text: '禁用', type: 'danger' }
        }
      },
      { prop: 'created_at', label: '创建时间', width: 120 }
    ])

    const demoPagination = ref({
      page: 1,
      pageSize: 10,
      total: 100
    })

    // 表单演示数据
    const demoFormFields = ref([
      {
        prop: 'name',
        label: '姓名',
        type: 'input',
        placeholder: '请输入姓名',
        required: true
      },
      {
        prop: 'age',
        label: '年龄',
        type: 'number',
        min: 0,
        max: 150
      },
      {
        prop: 'gender',
        label: '性别',
        type: 'radio',
        options: [
          { label: '男', value: 'male' },
          { label: '女', value: 'female' }
        ]
      },
      {
        prop: 'hobbies',
        label: '爱好',
        type: 'checkbox',
        options: [
          { label: '读书', value: 'reading' },
          { label: '运动', value: 'sports' },
          { label: '音乐', value: 'music' }
        ]
      },
      {
        prop: 'description',
        label: '描述',
        type: 'textarea',
        rows: 3,
        placeholder: '请输入描述信息'
      }
    ])

    const demoFormModel = reactive({
      name: '',
      age: null,
      gender: '',
      hobbies: [],
      description: ''
    })

    // 事件处理
    const handleDemoSearch = (searchData) => {
      console.log('搜索数据:', searchData)
    }

    const handleDemoReset = () => {
      console.log('重置搜索')
    }

    const handleRefresh = () => {
      console.log('刷新数据')
    }

    const handleFormSubmit = (formData) => {
      console.log('表单提交:', formData)
    }

    return {
      demoSearchFields,
      demoTableData,
      demoTableColumns,
      demoPagination,
      demoFormFields,
      demoFormModel,
      handleDemoSearch,
      handleDemoReset,
      handleRefresh,
      handleFormSubmit
    }
  }
})
</script>

<style scoped>
.demo-section {
  margin-bottom: var(--spacing-xxl);
}

.section-title {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
  margin-bottom: var(--spacing-lg);
  padding-bottom: var(--spacing-sm);
  border-bottom: 2px solid var(--primary-color);
}

.subsection {
  margin-bottom: var(--spacing-xl);
}

.subsection-title {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-medium);
  color: var(--text-primary);
  margin-bottom: var(--spacing-md);
}

/* 颜色展示 */
.color-palette {
  display: flex;
  gap: var(--spacing-xl);
  flex-wrap: wrap;
}

.color-group h5 {
  margin-bottom: var(--spacing-sm);
  color: var(--text-secondary);
}

.color-items {
  display: flex;
  gap: var(--spacing-md);
}

.color-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-xs);
}

.color-block {
  width: 60px;
  height: 60px;
  border-radius: var(--radius-md);
  box-shadow: var(--shadow-sm);
}

.color-item span {
  font-size: var(--font-size-xs);
  color: var(--text-secondary);
}

/* 字体展示 */
.typography-demo > div {
  margin-bottom: var(--spacing-sm);
  color: var(--text-primary);
}

/* 间距展示 */
.spacing-demo {
  display: flex;
  gap: var(--spacing-lg);
  align-items: center;
  flex-wrap: wrap;
}

.spacing-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.spacing-block {
  height: 20px;
  background: var(--primary-color);
  border-radius: var(--radius-sm);
}

/* 工具类展示 */
.utility-demo > div {
  margin-bottom: var(--spacing-sm);
}

.text-error {
  color: var(--error-color) !important;
}

/* 响应式 */
@media (max-width: 768px) {
  .color-palette {
    flex-direction: column;
  }
  
  .spacing-demo {
    flex-direction: column;
    align-items: flex-start;
  }
}
</style>

/* pages/index/index.wxss */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center; /* 垂直居中内容 */
  padding: 20px;
  padding-top: 40px; /* 减小顶部内边距 */
  background-color: #f8f8f8;
  min-height: 100vh;
  box-sizing: border-box; /* 确保padding不增加总宽度 */
  animation: fadeIn 0.5s ease-out; /* 应用淡入动画 */
}

.card {
  background-color: #fff;
  border-radius: 12px;
  padding: 25px;
  margin-bottom: 30px; /* 调整卡片之间的底部间距 */
  width: 100%;
  max-width: 350px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  transition: transform 0.2s ease-in-out;
}

.card:active {
  transform: translateY(2px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.card-icon {
  width: 60px;
  height: 60px;
  margin-bottom: 15px;
}

.card-title {
  font-size: 20px;
  font-weight: bold;
  color: #333;
  margin-bottom: 10px;
}

.card-description {
  font-size: 14px;
  color: #666;
  line-height: 1.5;
}

const { Patient, Image } = require('../models');
const { Op } = require('sequelize');

async function createPatient(patientData) {
  try {
    const patient = await Patient.create(patientData);
    return { success: true, patient };
  } catch (error) {
    console.error('服务层：创建患者失败:', error);
    return { success: false, message: error.message };
  }
}

async function getAllPatients(page, limit, search) {
  try {
    const offset = (page - 1) * limit;
    const whereClause = {};

    if (search) {
      whereClause[Op.or] = [
        { name: { [Op.like]: `%${search}%` } },
        { medical_history: { [Op.like]: `%${search}%` } },
        { symptoms: { [Op.like]: `%${search}%` } }
      ];
    }

    const { count, rows: patients } = await Patient.findAndCountAll({
      where: whereClause,
      limit: parseInt(limit),
      offset: parseInt(offset),
      order: [['created_at', 'DESC']]
    });

    return { success: true, total: count, patients };
  } catch (error) {
    console.error('服务层：获取患者列表失败:', error);
    return { success: false, message: error.message };
  }
}

async function getPatientById(id) {
  try {
    const patient = await Patient.findByPk(id, {
      include: [{
        model: Image,
        as: 'images'
      }]
    });
    if (!patient) {
      return { success: false, message: '未找到该患者信息。' };
    }
    return { success: true, patient };
  } catch (error) {
    console.error('服务层：获取患者详情失败:', error);
    return { success: false, message: error.message };
  }
}

module.exports = {
  createPatient,
  getAllPatients,
  getPatientById
};
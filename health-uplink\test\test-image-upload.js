// 测试图片上传功能
const FormData = require('form-data');
const fs = require('fs');
const path = require('path');
const axios = require('axios');

async function testImageUpload() {
  try {
    console.log('开始测试图片上传...');
    
    // 创建一个测试图片文件（如果不存在）
    const testImagePath = path.join(__dirname, 'test-image.png');
    if (!fs.existsSync(testImagePath)) {
      // 创建一个简单的测试图片（1x1像素的PNG）
      const pngData = Buffer.from([
        0x89, 0x50, 0x4E, 0x47, 0x0D, 0x0A, 0x1A, 0x0A, 0x00, 0x00, 0x00, 0x0D,
        0x49, 0x48, 0x44, 0x52, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x01,
        0x08, 0x02, 0x00, 0x00, 0x00, 0x90, 0x77, 0x53, 0xDE, 0x00, 0x00, 0x00,
        0x0C, 0x49, 0x44, 0x41, 0x54, 0x08, 0x99, 0x01, 0x01, 0x00, 0x00, 0x00,
        0xFF, 0xFF, 0x00, 0x00, 0x00, 0x02, 0x00, 0x01, 0xE2, 0x21, 0xBC, 0x33,
        0x00, 0x00, 0x00, 0x00, 0x49, 0x45, 0x4E, 0x44, 0xAE, 0x42, 0x60, 0x82
      ]);
      fs.writeFileSync(testImagePath, pngData);
      console.log('创建测试图片:', testImagePath);
    }
    
    // 创建表单数据
    const form = new FormData();
    form.append('images', fs.createReadStream(testImagePath), {
      filename: 'test-image.png',
      contentType: 'image/png'
    });
    form.append('image_type', '测试图片');
    
    console.log('发送上传请求...');
    
    // 发送请求
    const response = await axios.post('http://localhost:3000/api/image/upload', form, {
      headers: {
        ...form.getHeaders()
      },
      timeout: 10000
    });
    
    console.log('上传成功!');
    console.log('响应状态:', response.status);
    console.log('响应数据:', JSON.stringify(response.data, null, 2));
    
    // 清理测试文件
    if (fs.existsSync(testImagePath)) {
      fs.unlinkSync(testImagePath);
      console.log('清理测试文件');
    }
    
  } catch (error) {
    console.error('测试失败:');
    if (error.response) {
      console.error('响应状态:', error.response.status);
      console.error('响应数据:', error.response.data);
    } else {
      console.error('错误信息:', error.message);
    }
  }
}

// 运行测试
if (require.main === module) {
  testImageUpload();
}

module.exports = testImageUpload;

# Health Uplink API 测试脚本

Write-Host "=== Health Uplink API 测试 ===" -ForegroundColor Green

# 1. 测试登录获取Token
Write-Host "`n1. 测试登录..." -ForegroundColor Yellow
try {
    $loginBody = @{
        username = 'admin'
        password = 'admin123'
    } | ConvertTo-Json

    $loginResponse = Invoke-RestMethod -Uri 'http://localhost:3000/api/user/login' -Method POST -Body $loginBody -ContentType 'application/json'
    $token = $loginResponse.token
    Write-Host "登录成功，Token: $($token.Substring(0,20))..." -ForegroundColor Green
} catch {
    Write-Host "登录失败: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# 2. 测试获取患者列表
Write-Host "`n2. 测试获取患者列表..." -ForegroundColor Yellow
try {
    $headers = @{
        'Authorization' = "Bearer $token"
    }
    
    $patientsResponse = Invoke-RestMethod -Uri 'http://localhost:3000/api/patient/list' -Method GET -Headers $headers
    Write-Host "患者列表获取成功，共 $($patientsResponse.total) 个患者" -ForegroundColor Green
    
    if ($patientsResponse.patients.Count -gt 0) {
        $firstPatient = $patientsResponse.patients[0]
        Write-Host "第一个患者: $($firstPatient.name), ID: $($firstPatient.patient_id)" -ForegroundColor Cyan
        $testPatientId = $firstPatient.patient_id
    } else {
        Write-Host "没有患者数据，将使用ID=1进行测试" -ForegroundColor Yellow
        $testPatientId = 1
    }
} catch {
    Write-Host "获取患者列表失败: $($_.Exception.Message)" -ForegroundColor Red
    $testPatientId = 1
}

# 3. 测试获取患者详情
Write-Host "`n3. 测试获取患者详情..." -ForegroundColor Yellow
try {
    $patientDetailResponse = Invoke-RestMethod -Uri "http://localhost:3000/api/patient/$testPatientId" -Method GET -Headers $headers
    Write-Host "患者详情获取成功: $($patientDetailResponse.patient.name)" -ForegroundColor Green
} catch {
    Write-Host "获取患者详情失败: $($_.Exception.Message)" -ForegroundColor Red
}

# 4. 测试更新患者信息
Write-Host "`n4. 测试更新患者信息..." -ForegroundColor Yellow
try {
    $updateBody = @{
        name = '测试患者'
        age = 30
        medical_history = '测试病史'
        symptoms = '测试症状'
    } | ConvertTo-Json

    $updateResponse = Invoke-RestMethod -Uri "http://localhost:3000/api/patient/$testPatientId" -Method PUT -Body $updateBody -ContentType 'application/json' -Headers $headers
    Write-Host "患者信息更新成功: $($updateResponse.message)" -ForegroundColor Green
} catch {
    Write-Host "更新患者信息失败: $($_.Exception.Message)" -ForegroundColor Red
}

# 5. 测试健康检查
Write-Host "`n5. 测试系统健康检查..." -ForegroundColor Yellow
try {
    $healthResponse = Invoke-RestMethod -Uri 'http://localhost:3000/health' -Method GET
    Write-Host "健康检查结果: $($healthResponse.status)" -ForegroundColor Green
} catch {
    Write-Host "健康检查失败: $($_.Exception.Message)" -ForegroundColor Red
}

# 6. 测试系统状态
Write-Host "`n6. 测试系统状态..." -ForegroundColor Yellow
try {
    $statusResponse = Invoke-RestMethod -Uri 'http://localhost:3000/monitoring/status' -Method GET
    Write-Host "系统状态获取成功" -ForegroundColor Green
    Write-Host "内存使用: $([math]::Round($statusResponse.memory.heapUsed / 1024 / 1024, 2)) MB" -ForegroundColor Cyan
} catch {
    Write-Host "获取系统状态失败: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`n=== API 测试完成 ===" -ForegroundColor Green
Write-Host "`n前端访问地址:" -ForegroundColor Yellow
Write-Host "- 患者列表: http://localhost:5173/patients" -ForegroundColor Cyan
Write-Host "- 患者详情: http://localhost:5173/patient/$testPatientId" -ForegroundColor Cyan
Write-Host "- 患者编辑: http://localhost:5173/patient/$testPatientId/edit" -ForegroundColor Cyan
Write-Host "- 系统状态: http://localhost:5173/status" -ForegroundColor Cyan

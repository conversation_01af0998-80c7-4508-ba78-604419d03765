#!/bin/bash

# 数据库备份脚本
# 使用方法: ./backup.sh [database_name] [backup_type]
# backup_type: full (完整备份) | incremental (增量备份)

set -e  # 遇到错误立即退出

# 配置变量
DB_HOST=${MYSQL_HOST:-localhost}
DB_PORT=${MYSQL_PORT:-3306}
DB_NAME=${MYSQL_DATABASE:-health_uplink}
DB_USER=${MYSQL_USER:-health_user}
DB_PASSWORD=${MYSQL_PASSWORD:-health_password}

BACKUP_DIR="/backups"
BACKUP_TYPE=${2:-full}
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
RETENTION_DAYS=${BACKUP_RETENTION_DAYS:-30}

# 创建备份目录
mkdir -p "$BACKUP_DIR/full"
mkdir -p "$BACKUP_DIR/incremental"
mkdir -p "/log/scripts"

# 日志函数
log() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" | tee -a "/log/scripts/backup.log"
}

# 错误处理函数
error_exit() {
    log "ERROR: $1"
    exit 1
}

# 检查MySQL连接
check_mysql_connection() {
    log "检查MySQL连接..."
    mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" -e "SELECT 1;" > /dev/null 2>&1 || error_exit "无法连接到MySQL数据库"
    log "MySQL连接正常"
}

# 完整备份
full_backup() {
    local backup_file="$BACKUP_DIR/full/${DB_NAME}_full_${TIMESTAMP}.sql"
    local compressed_file="${backup_file}.gz"
    
    log "开始完整备份: $DB_NAME"
    
    # 执行备份
    mysqldump -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" \
        --single-transaction \
        --routines \
        --triggers \
        --events \
        --hex-blob \
        --master-data=2 \
        "$DB_NAME" > "$backup_file" || error_exit "完整备份失败"
    
    # 压缩备份文件
    gzip "$backup_file" || error_exit "压缩备份文件失败"
    
    # 计算文件大小和校验和
    local file_size=$(stat -c%s "$compressed_file")
    local checksum=$(md5sum "$compressed_file" | cut -d' ' -f1)
    
    log "完整备份完成: $compressed_file"
    log "文件大小: $(($file_size / 1024 / 1024)) MB"
    log "MD5校验和: $checksum"
    
    # 记录备份信息
    echo "${TIMESTAMP},full,${compressed_file},${file_size},${checksum}" >> "$BACKUP_DIR/backup_history.csv"
}

# 增量备份 (基于binlog)
incremental_backup() {
    local backup_file="$BACKUP_DIR/incremental/${DB_NAME}_incremental_${TIMESTAMP}.sql"
    local compressed_file="${backup_file}.gz"
    
    log "开始增量备份: $DB_NAME"
    
    # 获取最后一次完整备份的binlog位置
    local last_position_file="$BACKUP_DIR/last_binlog_position.txt"
    local start_position=""
    
    if [ -f "$last_position_file" ]; then
        start_position=$(cat "$last_position_file")
        log "从位置开始增量备份: $start_position"
    else
        log "警告: 未找到上次备份位置，执行完整备份"
        full_backup
        return
    fi
    
    # 获取当前binlog位置
    local current_position=$(mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" \
        -e "SHOW MASTER STATUS\G" | grep Position | awk '{print $2}')
    
    # 导出binlog
    mysqlbinlog --start-position="$start_position" \
        --stop-position="$current_position" \
        --database="$DB_NAME" \
        mysql-bin.* > "$backup_file" || error_exit "增量备份失败"
    
    # 压缩备份文件
    gzip "$backup_file" || error_exit "压缩增量备份文件失败"
    
    # 更新位置文件
    echo "$current_position" > "$last_position_file"
    
    local file_size=$(stat -c%s "$compressed_file")
    local checksum=$(md5sum "$compressed_file" | cut -d' ' -f1)
    
    log "增量备份完成: $compressed_file"
    log "文件大小: $(($file_size / 1024)) KB"
    log "MD5校验和: $checksum"
    
    # 记录备份信息
    echo "${TIMESTAMP},incremental,${compressed_file},${file_size},${checksum}" >> "$BACKUP_DIR/backup_history.csv"
}

# 清理旧备份
cleanup_old_backups() {
    log "清理 $RETENTION_DAYS 天前的备份文件..."
    
    # 清理完整备份
    find "$BACKUP_DIR/full" -name "*.sql.gz" -mtime +$RETENTION_DAYS -delete
    
    # 清理增量备份
    find "$BACKUP_DIR/incremental" -name "*.sql.gz" -mtime +$RETENTION_DAYS -delete
    
    # 清理日志文件
    find "$BACKUP_DIR/logs" -name "*.log" -mtime +$RETENTION_DAYS -delete
    
    log "清理完成"
}

# 验证备份文件
verify_backup() {
    local backup_file="$1"
    
    if [ ! -f "$backup_file" ]; then
        error_exit "备份文件不存在: $backup_file"
    fi
    
    # 检查文件是否为空
    if [ ! -s "$backup_file" ]; then
        error_exit "备份文件为空: $backup_file"
    fi
    
    # 验证gzip文件完整性
    if [[ "$backup_file" == *.gz ]]; then
        gzip -t "$backup_file" || error_exit "备份文件损坏: $backup_file"
    fi
    
    log "备份文件验证通过: $backup_file"
}

# 主函数
main() {
    log "开始数据库备份任务"
    log "备份类型: $BACKUP_TYPE"
    log "数据库: $DB_NAME"
    
    # 检查MySQL连接
    check_mysql_connection
    
    # 创建备份历史文件头部
    if [ ! -f "$BACKUP_DIR/backup_history.csv" ]; then
        echo "timestamp,type,file,size,checksum" > "$BACKUP_DIR/backup_history.csv"
    fi
    
    # 执行备份
    case "$BACKUP_TYPE" in
        "full")
            full_backup
            ;;
        "incremental")
            incremental_backup
            ;;
        *)
            error_exit "未知的备份类型: $BACKUP_TYPE"
            ;;
    esac
    
    # 清理旧备份
    cleanup_old_backups
    
    log "备份任务完成"
}

# 执行主函数
main "$@"

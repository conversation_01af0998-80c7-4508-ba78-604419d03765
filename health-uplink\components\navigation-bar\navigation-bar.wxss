/* components/navigation-bar/navigation-bar.wxss */
.navigation-bar {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  /* height will be set dynamically */
  background-color: #1890ff;
  color: #fff;
  display: flex;
  align-items: flex-end; /* Align items to the bottom */
  justify-content: center; /* Center the title */
  padding: 0 10px 5px 10px; /* Add padding to the bottom */
  box-sizing: border-box;
  z-index: 999;
}

.back-button {
  position: absolute;
  left: 10px;
  bottom: 6px;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(255, 255, 255, 0.3); /* Add a semi-transparent white background */
  border-radius: 50%; /* Make it circular */
}

.back-icon {
  width: 24px;
  height: 24px;
}

.home-button {
  position: absolute;
  right: 10px;
  bottom: 6px;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.home-icon {
  width: 24px;
  height: 24px;
}

.title {
  font-size: 18px;
  font-weight: bold;
  position: absolute; /* Position title absolutely to center it */
  left: 50%;
  transform: translateX(-50%);
  bottom: 6px;
}

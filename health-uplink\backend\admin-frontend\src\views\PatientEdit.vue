<template>
  <ContentContainer 
    :title="`编辑患者 - ${patient.name || '加载中...'}`"
    description="编辑患者的基本信息和医疗记录"
    :show-header="true"
    :breadcrumbs="breadcrumbs"
    :show-breadcrumb="true"
  >
    <!-- 操作按钮 -->
    <template #header-right>
      <el-button @click="handleCancel">
        取消
      </el-button>
      <el-button 
        type="primary" 
        @click="handleSave"
        :loading="saving"
      >
        保存
      </el-button>
    </template>

    <!-- 编辑表单 -->
    <div class="patient-edit-form">
      <el-card v-loading="loading">
        <template #header>
          <span>患者信息编辑</span>
        </template>
        
        <el-form 
          ref="formRef"
          :model="formData"
          :rules="formRules"
          label-width="100px"
          size="large"
        >
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="姓名" prop="name">
                <el-input 
                  v-model="formData.name"
                  placeholder="请输入患者姓名"
                  clearable
                />
              </el-form-item>
            </el-col>
            
            <el-col :span="12">
              <el-form-item label="年龄" prop="age">
                <el-input-number 
                  v-model="formData.age"
                  :min="0"
                  :max="150"
                  placeholder="请输入年龄"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
          </el-row>
          
          <el-form-item label="病史" prop="medical_history">
            <el-input 
              v-model="formData.medical_history"
              type="textarea"
              :rows="4"
              placeholder="请输入患者病史信息"
              maxlength="500"
              show-word-limit
            />
          </el-form-item>
          
          <el-form-item label="症状" prop="symptoms">
            <el-input 
              v-model="formData.symptoms"
              type="textarea"
              :rows="4"
              placeholder="请输入患者症状描述"
              maxlength="500"
              show-word-limit
            />
          </el-form-item>
        </el-form>
      </el-card>
    </div>

    <!-- 操作历史 -->
    <div class="patient-history-section">
      <el-card>
        <template #header>
          <span>编辑历史</span>
        </template>
        
        <el-timeline>
          <el-timeline-item
            :timestamp="formatDate(patient.created_at)"
            type="primary"
          >
            创建患者档案
          </el-timeline-item>
          <el-timeline-item
            :timestamp="formatDate(patient.updated_at)"
            type="success"
          >
            最后更新时间
          </el-timeline-item>
          <el-timeline-item
            :timestamp="formatDate(new Date())"
            type="warning"
          >
            当前编辑中...
          </el-timeline-item>
        </el-timeline>
      </el-card>
    </div>
  </ContentContainer>
</template>

<script>
import { defineComponent, ref, onMounted, computed, reactive } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import ContentContainer from '../components/ContentContainer.vue'
import api from '../api'

export default defineComponent({
  name: 'PatientEdit',
  components: {
    ContentContainer
  },
  setup() {
    const route = useRoute()
    const router = useRouter()
    
    const loading = ref(false)
    const saving = ref(false)
    const patient = ref({})
    const formRef = ref()
    
    // 表单数据
    const formData = reactive({
      name: '',
      age: null,
      medical_history: '',
      symptoms: ''
    })
    
    // 表单验证规则
    const formRules = {
      name: [
        { required: true, message: '请输入患者姓名', trigger: 'blur' },
        { min: 2, max: 50, message: '姓名长度在 2 到 50 个字符', trigger: 'blur' }
      ],
      age: [
        { required: true, message: '请输入患者年龄', trigger: 'blur' },
        { type: 'number', min: 0, max: 150, message: '年龄必须在 0 到 150 之间', trigger: 'blur' }
      ]
    }
    
    // 面包屑导航
    const breadcrumbs = computed(() => [
      { title: '首页', path: '/' },
      { title: '患者管理', path: '/patients' },
      { title: '患者详情', path: `/patient/${route.params.id}` },
      { title: '编辑患者', path: `/patient/${route.params.id}/edit` }
    ])
    
    // 获取患者详情
    const fetchPatientDetail = async () => {
      loading.value = true
      try {
        const response = await api.get(`/patient/${route.params.id}`)
        
        if (response.patient) {
          patient.value = response.patient
          // 填充表单数据
          formData.name = response.patient.name || ''
          formData.age = response.patient.age || null
          formData.medical_history = response.patient.medical_history || ''
          formData.symptoms = response.patient.symptoms || ''
        } else {
          ElMessage.error('获取患者详情失败')
        }
      } catch (error) {
        console.error('获取患者详情失败:', error)
        ElMessage.error('获取患者详情失败，请稍后重试')
      } finally {
        loading.value = false
      }
    }
    
    // 保存患者信息
    const handleSave = async () => {
      try {
        // 表单验证
        const valid = await formRef.value.validate()
        if (!valid) return
        
        saving.value = true
        
        const response = await api.put(`/patient/${route.params.id}`, formData)
        
        if (response.message) {
          ElMessage.success('患者信息更新成功')
          router.push(`/patient/${route.params.id}`)
        } else {
          ElMessage.error('更新失败')
        }
      } catch (error) {
        console.error('更新患者信息失败:', error)
        ElMessage.error('更新失败，请稍后重试')
      } finally {
        saving.value = false
      }
    }
    
    // 取消编辑
    const handleCancel = async () => {
      // 检查是否有未保存的更改
      const hasChanges = 
        formData.name !== (patient.value.name || '') ||
        formData.age !== (patient.value.age || null) ||
        formData.medical_history !== (patient.value.medical_history || '') ||
        formData.symptoms !== (patient.value.symptoms || '')
      
      if (hasChanges) {
        try {
          await ElMessageBox.confirm(
            '您有未保存的更改，确定要离开吗？',
            '确认离开',
            {
              confirmButtonText: '确定',
              cancelButtonText: '取消',
              type: 'warning',
            }
          )
        } catch {
          return // 用户取消离开
        }
      }
      
      router.push('/patients')
    }
    
    // 格式化日期
    const formatDate = (dateString) => {
      if (!dateString) return '-'
      return new Date(dateString).toLocaleString('zh-CN')
    }
    
    onMounted(() => {
      fetchPatientDetail()
    })
    
    return {
      loading,
      saving,
      patient,
      formRef,
      formData,
      formRules,
      breadcrumbs,
      handleSave,
      handleCancel,
      formatDate
    }
  }
})
</script>

<style scoped>
.patient-edit-form,
.patient-history-section {
  margin-bottom: var(--spacing-lg);
}

:deep(.el-form-item__label) {
  font-weight: var(--font-weight-medium);
  color: var(--text-primary);
}

:deep(.el-input__wrapper) {
  border-radius: var(--radius-md);
}

:deep(.el-textarea__inner) {
  border-radius: var(--radius-md);
}

:deep(.el-input-number) {
  width: 100%;
}

/* 响应式设计 */
@media (max-width: 768px) {
  :deep(.el-form-item) {
    margin-bottom: var(--spacing-md);
  }
  
  :deep(.el-col) {
    margin-bottom: var(--spacing-sm);
  }
}
</style>

# Health Uplink 后端管理系统开发计划

## 项目概述
Health Uplink 是一个健康信息收集系统，包含：
- 微信小程序端：患者信息收集和图片上传
- 后端管理系统：数据管理和导出
- 管理前端：数据查看和统计

---

## 第二部分：后端管理系统开发

### 阶段一：后端项目初始化

#### 1. 创建后端项目基础结构
- [x] 创建 `backend` 目录
- [x] 初始化 Node.js 项目 (`npm init`)
- [x] 创建项目目录结构（controllers、models、routes、services、middlewares、utils、config）
- [x] 安装基础依赖包：`express`, `mysql2`, `sequelize`, `multer`, `jsonwebtoken`, `cors`, `bcryptjs`, `dotenv`
- [x] 创建 `app.js` 主入口文件

#### 2. 配置数据库连接
- [x] 创建 `config/config.js` 数据库配置文件
- [x] 配置 MySQL 连接参数 (IP: ************, 端口: 3306, 用户名: root, 密码: zn123, 数据库名: health_db)
- [x] 创建 Sequelize 实例
- [x] 测试数据库连接

#### 3. 创建数据库模型
- [x] 创建患者信息模型 `models/patient.js` (patient_id, name, age, medical_history, symptoms, created_at, updated_at)
- [x] 创建图片资料模型 `models/image.js` (image_id, patient_id, image_url, image_type, created_at)
- [x] 创建医生信息模型 `models/doctor.js` (doctor_id, name, created_at)
- [x] 设置模型关联关系：Patient hasMany Images, Image belongsTo Patient
- [x] 创建数据库同步脚本

### 阶段二：API 接口开发

#### 4. 创建患者信息接口
- [x] 创建患者控制器 `controllers/patient.js`
- [x] 创建患者服务 `services/patient.js`
- [x] 创建患者路由 `routes/patient.js`
- [x] 实现患者信息提交接口 POST `/api/patient/info`
- [x] 实现患者信息查询接口 GET `/api/patient/list` (支持分页、搜索)
- [x] 实现患者详情接口 GET `/api/patient/:id`

#### 5. 创建图片上传接口
- [x] 配置 Multer 文件上传中间件
- [x] 创建图片控制器 `controllers/image.js`
- [x] 创建图片服务 `services/image.js`
- [x] 创建图片路由 `routes/image.js`
- [x] 实现图片上传接口 POST `/api/image/upload`
- [x] 实现图片列表接口 GET `/api/image/list` (支持分页、搜索)
- [x] 实现图片删除接口 DELETE `/api/image/:id`
- [x] 实现图片详情接口 GET `/api/image/:id`

#### 6. 创建数据导出接口
- [x] 创建导出控制器 `controllers/export.js`
- [x] 创建导出服务 `services/export.js`
- [x] 创建导出路由 `routes/export.js`
- [x] 实现 CSV 导出功能
- [x] 实现 ZIP 打包功能
- [x] 实现导出接口 GET `/api/export/data` (包含 `/api/export/csv`, `/api/export/images`, `/api/export/all`)

#### 7. 创建用户认证接口
- [x] 创建用户控制器 `controllers/user.js`
- [x] 创建用户服务 `services/user.js`
- [x] 创建用户路由 `routes/user.js`
- [x] 实现登录接口 POST `/api/user/login`
- [x] 实现权限验证中间件 `middlewares/auth.js`
- [x] 实现 JWT Token 生成和验证
- [x] 密码加密存储（使用 `bcryptjs`）
- [x] Token 验证和权限控制
- [x] 添加默认管理员账户的创建脚本

### 阶段三：前端管理界面开发

#### 8. 创建前端管理项目
- [x] 在 `backend` 目录下创建 `admin-frontend` 目录
- [x] 初始化 Vue 3 + Vite 项目
- [x] 安装 Element Plus、Vue Router、Axios、@element-plus/icons-vue 等依赖
- [x] 配置项目基础结构
- [x] 创建主要页面布局
- [x] 配置 Axios 基础请求拦截器

#### 9. 开发登录和主布局
- [x] 创建登录页面 `views/Login.vue`
- [x] 实现登录表单和逻辑
- [x] 创建主布局 `layout/MainLayout.vue`
- [x] 创建侧边栏导航和顶部导航栏
- [x] 实现路由守卫和权限控制
- [x] 响应式设计

#### 10. 开发业务页面
- [x] 创建患者管理页面 `views/PatientList.vue` 和 `views/PatientDetail.vue`
- [x] 创建图片管理页面 `views/ImageList.vue`
- [x] 创建数据导出页面 `views/DataExport.vue`
- [x] 实现数据表格、分页、搜索、筛选功能
- [x] 实现图片预览、删除功能
- [x] 实现数据导出和下载功能
- [x] 所有页面添加错误处理和用户反馈

### 阶段四：系统优化和完善

#### 11. 添加系统监控和日志
- [x] 集成日志系统 (winston)
- [x] 添加 API 访问日志
- [x] 实现错误日志记录
- [ ] 添加系统状态监控
- [x] 创建日志查看页面
- [ ] 实现日志文件轮转和清理

#### 12. 数据库优化和安全加固
- [x] 添加数据库索引，配置数据库连接池
- [x] 实现数据库自动备份功能
- [x] 实现 API 访问频率限制 (express-rate-limit)
- [ ] 添加输入数据验证和 XSS 防护
- [ ] 实现文件上传安全检查，操作审计日志

### 阶段五：测试和部署

#### 13. 创建测试套件
- [ ] 安装测试框架 (Jest, Supertest)
- [ ] 编写 API 接口测试
- [ ] 编写数据库操作测试
- [ ] 创建测试数据和模拟环境
- [ ] 执行完整功能测试

#### 14. 性能优化和部署准备
- [ ] 实现 API 响应缓存 (Redis)
- [ ] 优化图片处理性能 (上传后自动压缩和生成缩略图)
- [ ] 创建 Docker 配置文件 (Dockerfile, docker-compose.yml)
- [ ] 编写部署脚本
- [ ] 创建 Nginx 配置
- [ ] 编写自动化部署脚本

---

## 开发执行建议

### 项目启动顺序
1. **第1-2周**：后端基础框架 + 数据库设计 + API开发
2. **第3周**：前端管理界面开发
3. **第4周**：小程序开发
4. **第5周**：系统集成 + 完整测试
5. **第6周**：优化部署 + 文档完善

### 每日执行建议
- **上午**：集中时间完成一个完整模块
- **下午**：测试验证上午的开发成果
- **晚上**：文档更新和代码审查
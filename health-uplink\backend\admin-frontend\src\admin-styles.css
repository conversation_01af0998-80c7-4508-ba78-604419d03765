/* health-uplink/backend/admin-frontend/src/admin-styles.css */

/* 定义 CSS 变量 */
:root {
  --admin-primary-color: #1890ff; /* 主题蓝色 */
  --admin-success-color: #52c41a; /* 成功绿色 */
  --admin-warning-color: #faad14; /* 警告黄色 */
  --admin-error-color: #f5222d; /* 错误红色 */
  --admin-text-color: rgba(0, 0, 0, 0.85); /* 主要文本颜色 */
  --admin-text-color-secondary: rgba(0, 0, 0, 0.45); /* 次要文本颜色 */
  --admin-border-color: #d9d9d9; /* 边框颜色 */
  --admin-background-color: #f0f2f5; /* 页面背景颜色 */
  --admin-card-background: #fff; /* 卡片背景颜色 */
  --admin-padding-small: 12px;
  --admin-padding-medium: 20px;
  --admin-padding-large: 24px;
  --admin-margin-small: 8px;
  --admin-margin-medium: 16px;
  --admin-margin-large: 24px;
  --admin-border-radius: 4px;
}

/* 应用基础样式 */
body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  color: var(--admin-text-color);
  background-color: var(--admin-background-color);
  line-height: 1.5;
}

h1, h2, h3, h4, h5, h6 {
  color: var(--admin-text-color);
  margin-bottom: var(--admin-margin-medium);
}

/* 通用布局类 */
.admin-container {
  padding: var(--admin-padding-medium);
  max-width: 1200px; /* 示例最大宽度 */
  margin: 0 auto; /* 居中 */
}

.admin-card {
  background-color: var(--admin-card-background);
  border-radius: var(--admin-border-radius);
  padding: var(--admin-padding-medium);
  margin-bottom: var(--admin-margin-medium);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.09);
}

/* Element Plus 覆盖样式示例 (如果需要，可以在这里添加) */
/* .el-button--primary {
  background-color: var(--admin-primary-color);
  border-color: var(--admin-primary-color);
} */
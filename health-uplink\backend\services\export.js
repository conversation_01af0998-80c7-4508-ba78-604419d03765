const { Patient, Image } = require('../models');
const { createObjectCsvStringifier } = require('csv-writer');
const archiver = require('archiver');
const path = require('path');
const fs = require('fs');

async function exportPatientsToCsvService() {
  try {
    const patients = await Patient.findAll({
      order: [['created_at', 'ASC']]
    });

    const csvStringifier = createObjectCsvStringifier({
      header: [
        { id: 'patient_id', title: '患者ID' },
        { id: 'name', title: '姓名' },
        { id: 'age', title: '年龄' },
        { id: 'medical_history', title: '病史' },
        { id: 'symptoms', title: '症状' },
        { id: 'created_at', title: '创建时间' },
        { id: 'updated_at', title: '更新时间' }
      ]
    });

    const csvContent = csvStringifier.getHeaderString() + csvStringifier.stringifyRecords(patients);
    return { success: true, csvContent };
  } catch (error) {
    console.error('服务层：导出患者信息为 CSV 失败:', error);
    return { success: false, message: error.message };
  }
}

async function exportImagesToZipService() {
  try {
    const images = await Image.findAll();
    if (images.length === 0) {
      return { success: false, message: '没有图片可供导出。' };
    }

    const archive = archiver('zip', {
      zlib: { level: 9 }
    });

    // 创建一个可读流，将 archiver 的输出作为流返回
    const output = new (require('stream').PassThrough)();
    archive.pipe(output);

    for (const image of images) {
      const imagePath = path.join(__dirname, '..', image.image_url);
      if (fs.existsSync(imagePath)) {
        archive.file(imagePath, { name: path.basename(image.image_url) });
      } else {
        console.warn(`服务层：图片文件不存在: ${imagePath}`);
      }
    }

    archive.finalize();

    return { success: true, stream: output, filename: 'images.zip' };
  } catch (error) {
    console.error('服务层：导出图片为 ZIP 失败:', error);
    return { success: false, message: error.message };
  }
}

async function exportAllDataService() {
  try {
    const patients = await Patient.findAll({
      order: [['created_at', 'ASC']]
    });
    const csvStringifier = createObjectCsvStringifier({
      header: [
        { id: 'patient_id', title: '患者ID' },
        { id: 'name', title: '姓名' },
        { id: 'age', title: '年龄' },
        { id: 'medical_history', title: '病史' },
        { id: 'symptoms', title: '症状' },
        { id: 'created_at', title: '创建时间' },
        { id: 'updated_at', title: '更新时间' }
      ]
    });
    const csvContent = csvStringifier.getHeaderString() + csvStringifier.stringifyRecords(patients);

    const archive = archiver('zip', {
      zlib: { level: 9 }
    });

    const output = new (require('stream').PassThrough)();
    archive.pipe(output);

    archive.append(csvContent, { name: 'patients.csv' });

    const images = await Image.findAll();
    for (const image of images) {
      const imagePath = path.join(__dirname, '..', image.image_url);
      if (fs.existsSync(imagePath)) {
        archive.file(imagePath, { name: `images/${path.basename(image.image_url)}` });
      } else {
        console.warn(`服务层：图片文件不存在 (完整导出): ${imagePath}`);
      }
    }

    archive.finalize();

    return { success: true, stream: output, filename: 'health_data_full.zip' };
  } catch (error) {
    console.error('服务层：导出完整数据包失败:', error);
    return { success: false, message: error.message };
  }
}

module.exports = {
  exportPatientsToCsvService,
  exportImagesToZipService,
  exportAllDataService
};
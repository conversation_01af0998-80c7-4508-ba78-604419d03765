const express = require('express');
const router = express.Router();
const backupService = require('../services/backup');
const { authenticateToken } = require('../middlewares/auth');
const logger = require('../utils/logger');

// 所有备份相关路由都需要认证
router.use(authenticateToken);

// 获取备份列表
router.get('/list', async (req, res) => {
  try {
    const backups = await backupService.getBackupList();
    res.json({
      code: 0,
      message: 'Success',
      data: backups
    });
  } catch (error) {
    logger.error('Failed to get backup list:', error);
    res.status(500).json({
      code: 1,
      message: 'Failed to get backup list',
      error: error.message
    });
  }
});

// 创建备份
router.post('/create', async (req, res) => {
  try {
    const { type = 'full' } = req.body;
    
    if (!['full', 'incremental'].includes(type)) {
      return res.status(400).json({
        code: 1,
        message: 'Invalid backup type. Must be "full" or "incremental"'
      });
    }

    const result = await backupService.performBackup(type);
    
    if (result.success) {
      res.json({
        code: 0,
        message: result.message,
        data: {
          type,
          timestamp: result.timestamp,
          duration: result.duration
        }
      });
    } else {
      res.status(500).json({
        code: 1,
        message: result.message,
        error: result.error
      });
    }
  } catch (error) {
    logger.error('Failed to create backup:', error);
    res.status(500).json({
      code: 1,
      message: 'Failed to create backup',
      error: error.message
    });
  }
});

// 删除备份
router.delete('/:filename', async (req, res) => {
  try {
    const { filename } = req.params;
    
    if (!filename) {
      return res.status(400).json({
        code: 1,
        message: 'Filename is required'
      });
    }

    const result = await backupService.deleteBackup(filename);
    
    res.json({
      code: 0,
      message: result.message
    });
  } catch (error) {
    logger.error('Failed to delete backup:', error);
    res.status(500).json({
      code: 1,
      message: 'Failed to delete backup',
      error: error.message
    });
  }
});

// 恢复数据库
router.post('/restore', async (req, res) => {
  try {
    const { filename, targetDatabase } = req.body;
    
    if (!filename) {
      return res.status(400).json({
        code: 1,
        message: 'Filename is required'
      });
    }

    if (!targetDatabase) {
      return res.status(400).json({
        code: 1,
        message: 'Target database name is required'
      });
    }

    const result = await backupService.restoreDatabase(filename, targetDatabase);
    
    res.json({
      code: 0,
      message: result.message,
      data: {
        filename,
        targetDatabase,
        output: result.output
      }
    });
  } catch (error) {
    logger.error('Failed to restore database:', error);
    res.status(500).json({
      code: 1,
      message: 'Failed to restore database',
      error: error.message
    });
  }
});

// 验证备份文件
router.post('/verify/:filename', async (req, res) => {
  try {
    const { filename } = req.params;
    
    if (!filename) {
      return res.status(400).json({
        code: 1,
        message: 'Filename is required'
      });
    }

    const result = await backupService.verifyBackup(filename);
    
    res.json({
      code: 0,
      message: result.message,
      data: {
        filename: result.filename,
        size: result.size,
        valid: true
      }
    });
  } catch (error) {
    logger.error('Failed to verify backup:', error);
    res.status(500).json({
      code: 1,
      message: 'Failed to verify backup',
      error: error.message
    });
  }
});

// 获取备份历史
router.get('/history', (req, res) => {
  try {
    const history = backupService.getBackupHistory();
    res.json({
      code: 0,
      message: 'Success',
      data: history
    });
  } catch (error) {
    logger.error('Failed to get backup history:', error);
    res.status(500).json({
      code: 1,
      message: 'Failed to get backup history',
      error: error.message
    });
  }
});

// 获取备份状态
router.get('/status', (req, res) => {
  try {
    const status = backupService.getBackupStatus();
    res.json({
      code: 0,
      message: 'Success',
      data: status
    });
  } catch (error) {
    logger.error('Failed to get backup status:', error);
    res.status(500).json({
      code: 1,
      message: 'Failed to get backup status',
      error: error.message
    });
  }
});

// 下载备份文件
router.get('/download/:filename', async (req, res) => {
  try {
    const { filename } = req.params;
    
    if (!filename) {
      return res.status(400).json({
        code: 1,
        message: 'Filename is required'
      });
    }

    const backups = await backupService.getBackupList();
    const backup = backups.find(b => b.filename === filename);
    
    if (!backup) {
      return res.status(404).json({
        code: 1,
        message: 'Backup file not found'
      });
    }

    // 设置下载头
    res.setHeader('Content-Disposition', `attachment; filename="${filename}"`);
    res.setHeader('Content-Type', 'application/gzip');
    res.setHeader('Content-Length', backup.size);
    
    // 发送文件
    res.sendFile(backup.path);
  } catch (error) {
    logger.error('Failed to download backup:', error);
    res.status(500).json({
      code: 1,
      message: 'Failed to download backup',
      error: error.message
    });
  }
});

module.exports = router;

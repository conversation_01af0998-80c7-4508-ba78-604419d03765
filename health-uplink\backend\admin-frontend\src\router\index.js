import { createRouter, createWebHistory } from 'vue-router';
import Login from '../views/Login.vue';
import MainLayout from '../layout/MainLayout.vue';

const routes = [
  {
    path: '/login',
    name: 'Login',
    component: Login
  },
  {
    path: '/',
    name: 'Layout',
    component: MainLayout,
    children: [
      // 这里将添加业务页面的路由
      {
        path: 'patients',
        name: 'PatientList',
        component: () => import('../views/PatientList.vue')
      },
      {
        path: 'patient/:id',
        name: 'PatientDetail',
        component: () => import('../views/PatientDetail.vue')
      },
      {
        path: 'patient/:id/edit',
        name: 'PatientEdit',
        component: () => import('../views/PatientEdit.vue')
      },
      {
        path: 'image',
        name: 'ImageList',
        component: () => import('../views/ImageList.vue')
      },
      {
        path: 'image-debug',
        name: 'ImageListDebug',
        component: () => import('../views/ImageListDebug.vue')
      },
      {
        path: 'export',
        name: 'DataExport',
        component: () => import('../views/DataExport.vue')
      },
      {
        path: 'components',
        name: 'ComponentDemo',
        component: () => import('../views/ComponentDemo.vue')
      },
      {
        path: 'status',
        name: 'SystemStatus',
        component: () => import('../views/SystemStatus.vue')
      }
    ]
  }
];

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL), // 使用 HTML5 History 模式
  routes
});

// 路由守卫
router.beforeEach((to, from, next) => {
  const publicPages = ['/login']; // 不需要认证的页面
  const authRequired = !publicPages.includes(to.path);
  const loggedIn = localStorage.getItem('token'); // 检查本地存储是否有 token

  if (authRequired && !loggedIn) {
    // 如果需要认证但未登录，重定向到登录页
    return next('/login');
  }

  next(); // 继续导航
});

export default router;
<template>
  <div class="standard-form-container">
    <el-form
      ref="formRef"
      :model="formModel"
      :rules="formRules"
      :label-width="labelWidth"
      :label-position="labelPosition"
      :size="size"
      :disabled="disabled"
      :validate-on-rule-change="validateOnRuleChange"
      :hide-required-asterisk="hideRequiredAsterisk"
      :show-message="showMessage"
      :inline-message="inlineMessage"
      :status-icon="statusIcon"
      @validate="handleValidate"
    >
      <!-- 动态渲染表单项 -->
      <template v-for="field in formFields" :key="field.prop">
        <!-- 分组标题 -->
        <div v-if="field.type === 'group'" class="form-group">
          <div class="group-title">{{ field.label }}</div>
          <div class="group-content">
            <StandardForm
              v-if="field.fields"
              :form-fields="field.fields"
              :form-model="formModel"
              :form-rules="formRules"
              :label-width="field.labelWidth || labelWidth"
              :inline="field.inline || false"
              @validate="handleValidate"
            />
          </div>
        </div>
        
        <!-- 普通表单项 -->
        <el-form-item
          v-else
          :prop="field.prop"
          :label="field.label"
          :required="field.required"
          :rules="field.rules"
          :error="field.error"
          :show-message="field.showMessage !== false"
          :inline-message="field.inlineMessage"
          :size="field.size || size"
        >
          <!-- 输入框 -->
          <el-input
            v-if="field.type === 'input'"
            v-model="formModel[field.prop]"
            :type="field.inputType || 'text'"
            :placeholder="field.placeholder"
            :clearable="field.clearable !== false"
            :show-password="field.showPassword"
            :disabled="field.disabled"
            :readonly="field.readonly"
            :maxlength="field.maxlength"
            :minlength="field.minlength"
            :show-word-limit="field.showWordLimit"
            :prefix-icon="field.prefixIcon"
            :suffix-icon="field.suffixIcon"
            @blur="handleFieldBlur(field)"
            @focus="handleFieldFocus(field)"
            @change="handleFieldChange(field)"
          />
          
          <!-- 文本域 -->
          <el-input
            v-else-if="field.type === 'textarea'"
            v-model="formModel[field.prop]"
            type="textarea"
            :placeholder="field.placeholder"
            :rows="field.rows || 3"
            :autosize="field.autosize"
            :disabled="field.disabled"
            :readonly="field.readonly"
            :maxlength="field.maxlength"
            :show-word-limit="field.showWordLimit"
            @blur="handleFieldBlur(field)"
            @focus="handleFieldFocus(field)"
            @change="handleFieldChange(field)"
          />
          
          <!-- 选择器 -->
          <el-select
            v-else-if="field.type === 'select'"
            v-model="formModel[field.prop]"
            :placeholder="field.placeholder"
            :clearable="field.clearable !== false"
            :disabled="field.disabled"
            :multiple="field.multiple"
            :filterable="field.filterable"
            :allow-create="field.allowCreate"
            :remote="field.remote"
            :remote-method="field.remoteMethod"
            :loading="field.loading"
            @change="handleFieldChange(field)"
            @visible-change="handleSelectVisibleChange(field, $event)"
          >
            <el-option
              v-for="option in field.options"
              :key="option.value"
              :label="option.label"
              :value="option.value"
              :disabled="option.disabled"
            />
          </el-select>
          
          <!-- 日期选择器 -->
          <el-date-picker
            v-else-if="field.type === 'date'"
            v-model="formModel[field.prop]"
            :type="field.dateType || 'date'"
            :placeholder="field.placeholder"
            :start-placeholder="field.startPlaceholder"
            :end-placeholder="field.endPlaceholder"
            :range-separator="field.rangeSeparator || '至'"
            :disabled="field.disabled"
            :clearable="field.clearable !== false"
            :format="field.format"
            :value-format="field.valueFormat"
            @change="handleFieldChange(field)"
          />
          
          <!-- 数字输入框 -->
          <el-input-number
            v-else-if="field.type === 'number'"
            v-model="formModel[field.prop]"
            :min="field.min"
            :max="field.max"
            :step="field.step || 1"
            :precision="field.precision"
            :disabled="field.disabled"
            :controls="field.controls !== false"
            :controls-position="field.controlsPosition"
            @change="handleFieldChange(field)"
          />
          
          <!-- 开关 -->
          <el-switch
            v-else-if="field.type === 'switch'"
            v-model="formModel[field.prop]"
            :disabled="field.disabled"
            :active-text="field.activeText"
            :inactive-text="field.inactiveText"
            :active-value="field.activeValue"
            :inactive-value="field.inactiveValue"
            @change="handleFieldChange(field)"
          />
          
          <!-- 单选框组 -->
          <el-radio-group
            v-else-if="field.type === 'radio'"
            v-model="formModel[field.prop]"
            :disabled="field.disabled"
            @change="handleFieldChange(field)"
          >
            <el-radio
              v-for="option in field.options"
              :key="option.value"
              :value="option.value"
              :disabled="option.disabled"
            >
              {{ option.label }}
            </el-radio>
          </el-radio-group>
          
          <!-- 复选框组 -->
          <el-checkbox-group
            v-else-if="field.type === 'checkbox'"
            v-model="formModel[field.prop]"
            :disabled="field.disabled"
            @change="handleFieldChange(field)"
          >
            <el-checkbox
              v-for="option in field.options"
              :key="option.value"
              :label="option.value"
              :disabled="option.disabled"
            >
              {{ option.label }}
            </el-checkbox>
          </el-checkbox-group>
          
          <!-- 文件上传 -->
          <el-upload
            v-else-if="field.type === 'upload'"
            :action="field.action"
            :headers="field.headers"
            :data="field.data"
            :name="field.name || 'file'"
            :multiple="field.multiple"
            :accept="field.accept"
            :limit="field.limit"
            :file-list="formModel[field.prop] || []"
            :disabled="field.disabled"
            :auto-upload="field.autoUpload !== false"
            :list-type="field.listType || 'text'"
            :on-success="(response, file, fileList) => handleUploadSuccess(field, response, file, fileList)"
            :on-error="(error, file, fileList) => handleUploadError(field, error, file, fileList)"
            :on-remove="(file, fileList) => handleUploadRemove(field, file, fileList)"
            :before-upload="(file) => handleBeforeUpload(field, file)"
          >
            <el-button v-if="field.listType !== 'picture-card'" type="primary">
              {{ field.uploadText || '选择文件' }}
            </el-button>
            <el-icon v-else><Plus /></el-icon>
          </el-upload>
          
          <!-- 自定义插槽 -->
          <slot
            v-else-if="field.type === 'slot'"
            :name="field.slot"
            :field="field"
            :model="formModel"
          />
          
          <!-- 帮助文本 -->
          <div v-if="field.help" class="field-help">
            {{ field.help }}
          </div>
        </el-form-item>
      </template>
      
      <!-- 表单操作按钮 -->
      <el-form-item v-if="showActions" class="form-actions">
        <slot name="actions" :model="formModel" :validate="validate" :reset="resetForm">
          <el-button 
            type="primary" 
            @click="handleSubmit"
            :loading="submitLoading"
          >
            {{ submitText }}
          </el-button>
          <el-button @click="handleReset">
            {{ resetText }}
          </el-button>
          <el-button v-if="showCancel" @click="handleCancel">
            {{ cancelText }}
          </el-button>
        </slot>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import { defineComponent, ref, reactive, watch } from 'vue'
import { Plus } from '@element-plus/icons-vue'

export default defineComponent({
  name: 'StandardForm',
  components: {
    Plus
  },
  props: {
    // 表单字段配置
    formFields: {
      type: Array,
      required: true
    },
    // 表单数据模型
    formModel: {
      type: Object,
      default: () => ({})
    },
    // 表单验证规则
    formRules: {
      type: Object,
      default: () => ({})
    },
    // 标签宽度
    labelWidth: {
      type: String,
      default: '120px'
    },
    // 标签位置
    labelPosition: {
      type: String,
      default: 'right'
    },
    // 表单尺寸
    size: {
      type: String,
      default: 'default'
    },
    // 是否禁用
    disabled: {
      type: Boolean,
      default: false
    },
    // 是否显示操作按钮
    showActions: {
      type: Boolean,
      default: true
    },
    // 是否显示取消按钮
    showCancel: {
      type: Boolean,
      default: false
    },
    // 提交按钮文本
    submitText: {
      type: String,
      default: '提交'
    },
    // 重置按钮文本
    resetText: {
      type: String,
      default: '重置'
    },
    // 取消按钮文本
    cancelText: {
      type: String,
      default: '取消'
    },
    // 提交加载状态
    submitLoading: {
      type: Boolean,
      default: false
    },
    // 其他表单属性
    validateOnRuleChange: {
      type: Boolean,
      default: true
    },
    hideRequiredAsterisk: {
      type: Boolean,
      default: false
    },
    showMessage: {
      type: Boolean,
      default: true
    },
    inlineMessage: {
      type: Boolean,
      default: false
    },
    statusIcon: {
      type: Boolean,
      default: false
    }
  },
  emits: ['submit', 'reset', 'cancel', 'validate', 'field-change'],
  setup(props, { emit }) {
    const formRef = ref()
    
    // 表单验证
    const validate = async () => {
      if (!formRef.value) return false
      try {
        await formRef.value.validate()
        return true
      } catch (error) {
        return false
      }
    }
    
    // 重置表单
    const resetForm = () => {
      if (formRef.value) {
        formRef.value.resetFields()
      }
    }
    
    // 清除验证
    const clearValidate = (props) => {
      if (formRef.value) {
        formRef.value.clearValidate(props)
      }
    }
    
    // 事件处理
    const handleSubmit = async () => {
      const isValid = await validate()
      if (isValid) {
        emit('submit', props.formModel)
      }
    }
    
    const handleReset = () => {
      resetForm()
      emit('reset')
    }
    
    const handleCancel = () => {
      emit('cancel')
    }
    
    const handleValidate = (prop, isValid, message) => {
      emit('validate', prop, isValid, message)
    }
    
    const handleFieldChange = (field) => {
      emit('field-change', field, props.formModel[field.prop])
    }
    
    const handleFieldBlur = (field) => {
      if (field.onBlur) {
        field.onBlur(props.formModel[field.prop], props.formModel)
      }
    }
    
    const handleFieldFocus = (field) => {
      if (field.onFocus) {
        field.onFocus(props.formModel[field.prop], props.formModel)
      }
    }
    
    const handleSelectVisibleChange = (field, visible) => {
      if (field.onVisibleChange) {
        field.onVisibleChange(visible, props.formModel)
      }
    }
    
    // 文件上传处理
    const handleUploadSuccess = (field, response, file, fileList) => {
      props.formModel[field.prop] = fileList
      if (field.onSuccess) {
        field.onSuccess(response, file, fileList)
      }
    }
    
    const handleUploadError = (field, error, file, fileList) => {
      if (field.onError) {
        field.onError(error, file, fileList)
      }
    }
    
    const handleUploadRemove = (field, file, fileList) => {
      props.formModel[field.prop] = fileList
      if (field.onRemove) {
        field.onRemove(file, fileList)
      }
    }
    
    const handleBeforeUpload = (field, file) => {
      if (field.beforeUpload) {
        return field.beforeUpload(file)
      }
      return true
    }
    
    return {
      formRef,
      validate,
      resetForm,
      clearValidate,
      handleSubmit,
      handleReset,
      handleCancel,
      handleValidate,
      handleFieldChange,
      handleFieldBlur,
      handleFieldFocus,
      handleSelectVisibleChange,
      handleUploadSuccess,
      handleUploadError,
      handleUploadRemove,
      handleBeforeUpload
    }
  }
})
</script>

<style scoped>
.standard-form-container {
  background: var(--bg-primary);
  border-radius: var(--radius-lg);
  padding: var(--spacing-lg);
  box-shadow: var(--shadow-sm);
}

.form-group {
  margin-bottom: var(--spacing-lg);
}

.group-title {
  font-size: var(--font-size-md);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
  margin-bottom: var(--spacing-md);
  padding-bottom: var(--spacing-sm);
  border-bottom: 1px solid var(--border-secondary);
}

.group-content {
  padding-left: var(--spacing-md);
}

.form-actions {
  margin-top: var(--spacing-xl);
  text-align: center;
}

.field-help {
  font-size: var(--font-size-xs);
  color: var(--text-tertiary);
  margin-top: var(--spacing-xs);
  line-height: var(--line-height-sm);
}

:deep(.el-form-item__label) {
  color: var(--text-secondary);
  font-weight: var(--font-weight-medium);
}

:deep(.el-form-item__error) {
  color: var(--error-color);
  font-size: var(--font-size-xs);
}

:deep(.el-input__wrapper) {
  border-radius: var(--radius-sm);
  transition: var(--transition-normal);
}

:deep(.el-input__wrapper:hover) {
  border-color: var(--primary-color);
}

:deep(.el-input__wrapper.is-focus) {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

:deep(.el-select .el-input__wrapper) {
  cursor: pointer;
}

:deep(.el-textarea__inner) {
  border-radius: var(--radius-sm);
  transition: var(--transition-normal);
}

:deep(.el-textarea__inner:hover) {
  border-color: var(--primary-color);
}

:deep(.el-textarea__inner:focus) {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}
</style>

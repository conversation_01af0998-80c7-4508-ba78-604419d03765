const fs = require('fs');
const path = require('path');

const logDir = path.join(__dirname, '../../log/backend'); // 日志文件存储目录

// 读取日志文件内容
async function readLogFile(filename) {
  const filePath = path.join(logDir, filename);

  // 简单的安全检查，防止目录遍历
  if (!filePath.startsWith(logDir)) {
      return { success: false, message: '无效的文件名。' };
  }

  try {
    // 检查文件是否存在
    if (!fs.existsSync(filePath)) {
      return { success: false, message: `日志文件 ${filename} 不存在。` };
    }

    const content = await fs.promises.readFile(filePath, 'utf8');
    return { success: true, content };
  } catch (error) {
    console.error(`服务层：读取日志文件 ${filename} 失败:`, error);
    return { success: false, message: `读取日志文件 ${filename} 失败`, error: error.message };
  }
}

// 列出可用日志文件
async function listLogFiles() {
  try {
    // 确保日志目录存在
    if (!fs.existsSync(logDir)) {
        return { success: true, files: [] };
    }
    const files = await fs.promises.readdir(logDir);
    // 可以根据需要过滤文件，例如只返回 .log 文件
    const logFiles = files.filter(file => file.endsWith('.log'));
    return { success: true, files: logFiles };
  } catch (error) {
    console.error('服务层：列出日志文件失败:', error);
    return { success: false, message: '列出日志文件失败', error: error.message };
  }
}

module.exports = {
  readLogFile,
  listLogFiles
};
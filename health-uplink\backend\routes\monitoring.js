const express = require('express');
const router = express.Router();
const { getMetrics, getSystemStatus } = require('../middlewares/monitoring');
const { sequelize } = require('../config/config');
const logger = require('../utils/logger');

// Prometheus指标端点
router.get('/metrics', async (req, res) => {
  try {
    res.set('Content-Type', 'text/plain');
    const metrics = await getMetrics();
    res.send(metrics);
  } catch (error) {
    logger.error('Failed to get metrics:', error);
    res.status(500).send('Failed to get metrics');
  }
});

// 系统状态端点
router.get('/status', async (req, res) => {
  try {
    const systemStatus = getSystemStatus();
    
    // 检查数据库连接
    let dbStatus = 'disconnected';
    try {
      await sequelize.authenticate();
      dbStatus = 'connected';
    } catch (error) {
      dbStatus = 'error';
    }
    
    const status = {
      ...systemStatus,
      database: dbStatus,
      version: process.env.npm_package_version || '1.0.0',
      environment: process.env.NODE_ENV || 'development'
    };
    
    res.json(status);
  } catch (error) {
    logger.error('Failed to get system status:', error);
    res.status(500).json({ error: 'Failed to get system status' });
  }
});

// 详细健康检查
router.get('/health/detailed', async (req, res) => {
  const checks = {
    database: false,
    memory: false,
    disk: false,
    uptime: false
  };
  
  let overallHealth = true;
  
  try {
    // 数据库检查
    await sequelize.authenticate();
    checks.database = true;
  } catch (error) {
    checks.database = false;
    overallHealth = false;
    logger.error('Database health check failed:', error);
  }
  
  // 内存检查 (如果使用内存超过90%则认为不健康)
  const memUsage = process.memoryUsage();
  const memoryUsagePercent = (memUsage.heapUsed / memUsage.heapTotal) * 100;
  checks.memory = memoryUsagePercent < 90;
  if (!checks.memory) {
    overallHealth = false;
  }
  
  // 运行时间检查 (运行时间小于5秒可能表示刚重启)
  checks.uptime = process.uptime() > 5;
  if (!checks.uptime) {
    overallHealth = false;
  }
  
  // 磁盘空间检查 (简化版本，实际应用中可以使用fs模块检查)
  checks.disk = true; // 简化实现
  
  const healthStatus = {
    status: overallHealth ? 'healthy' : 'unhealthy',
    timestamp: new Date().toISOString(),
    checks,
    details: {
      uptime: process.uptime(),
      memoryUsage: memoryUsagePercent.toFixed(2) + '%',
      version: process.env.npm_package_version || '1.0.0'
    }
  };
  
  res.status(overallHealth ? 200 : 503).json(healthStatus);
});

// 应用信息端点
router.get('/info', (req, res) => {
  const info = {
    name: 'Health Uplink Backend',
    version: process.env.npm_package_version || '1.0.0',
    description: '健康信息收集系统后端服务',
    environment: process.env.NODE_ENV || 'development',
    nodeVersion: process.version,
    platform: process.platform,
    architecture: process.arch,
    startTime: new Date(Date.now() - process.uptime() * 1000).toISOString(),
    uptime: process.uptime()
  };
  
  res.json(info);
});

module.exports = router;

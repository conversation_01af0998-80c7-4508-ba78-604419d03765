const Patient = require('./patient');
const Image = require('./image');
const Doctor = require('./doctor'); // 引入 Doctor 模型，尽管目前没有直接关联

// 设置模型关联关系
// 一个患者可以有多张图片
Patient.hasMany(Image, {
  foreignKey: 'patient_id',
  as: 'images' // 定义别名，方便查询时使用
});

// 一张图片属于一个患者
Image.belongsTo(Patient, {
  foreignKey: 'patient_id',
  as: 'patient' // 定义别名
});

// 导出所有模型
module.exports = {
  Patient,
  Image,
  Doctor
};
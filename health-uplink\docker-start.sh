#!/bin/bash

# Health Uplink Docker 启动脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log() {
    echo -e "${GREEN}[$(date '+%Y-%m-%d %H:%M:%S')]${NC} $1"
}

warn() {
    echo -e "${YELLOW}[$(date '+%Y-%m-%d %H:%M:%S')] WARNING:${NC} $1"
}

error() {
    echo -e "${RED}[$(date '+%Y-%m-%d %H:%M:%S')] ERROR:${NC} $1"
}

# 检查Docker和Docker Compose
check_requirements() {
    log "检查系统要求..."
    
    if ! command -v docker &> /dev/null; then
        error "Docker 未安装，请先安装 Docker"
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        error "Docker Compose 未安装，请先安装 Docker Compose"
        exit 1
    fi
    
    log "系统要求检查通过"
}

# 检查环境配置文件
check_env_file() {
    if [ ! -f ".env" ]; then
        if [ -f ".env.docker" ]; then
            log "复制 .env.docker 到 .env"
            cp .env.docker .env
        else
            warn "未找到 .env 文件，将使用默认配置"
            cat > .env << EOF
MYSQL_ROOT_PASSWORD=rootpassword123
MYSQL_DATABASE=health_uplink
MYSQL_USER=health_user
MYSQL_PASSWORD=health_password123
JWT_SECRET=your_jwt_secret_change_in_production
NODE_ENV=production
EOF
        fi
    fi
    log "环境配置文件检查完成"
}

# 创建必要的目录
create_directories() {
    log "创建必要的目录..."
    
    mkdir -p backups/full
    mkdir -p backups/incremental
    mkdir -p log/scripts
    mkdir -p mysql/data
    mkdir -p mysql/logs
    mkdir -p upload/images
    mkdir -p log/backend
    
    # 设置脚本执行权限
    chmod +x scripts/*.sh
    
    log "目录创建完成"
}

# 构建镜像
build_images() {
    log "构建 Docker 镜像..."
    
    docker-compose build --no-cache
    
    log "镜像构建完成"
}

# 启动服务
start_services() {
    log "启动服务..."
    
    # 先启动数据库
    log "启动 MySQL 数据库..."
    docker-compose up -d mysql redis
    
    # 等待数据库启动
    log "等待数据库启动..."
    sleep 30
    
    # 检查数据库连接
    local max_attempts=30
    local attempt=1
    
    while [ $attempt -le $max_attempts ]; do
        if docker-compose exec -T mysql mysql -h localhost -u root -p${MYSQL_ROOT_PASSWORD:-rootpassword123} -e "SELECT 1;" &> /dev/null; then
            log "数据库连接成功"
            break
        fi
        
        if [ $attempt -eq $max_attempts ]; then
            error "数据库启动失败"
            exit 1
        fi
        
        log "等待数据库启动... (尝试 $attempt/$max_attempts)"
        sleep 5
        ((attempt++))
    done
    
    # 启动后端服务
    log "启动后端服务..."
    docker-compose up -d backend
    
    # 等待后端服务启动
    sleep 10
    
    # 启动前端服务
    log "启动前端服务..."
    docker-compose up -d admin-frontend
    
    log "所有服务启动完成"
}

# 显示服务状态
show_status() {
    log "服务状态:"
    docker-compose ps
    
    echo ""
    log "服务访问地址:"
    echo -e "  ${BLUE}管理后台:${NC} http://localhost"
    echo -e "  ${BLUE}后端API:${NC} http://localhost:3000"
    echo -e "  ${BLUE}健康检查:${NC} http://localhost:3000/health"
    echo -e "  ${BLUE}监控指标:${NC} http://localhost:3000/monitoring/metrics"
    echo -e "  ${BLUE}系统状态:${NC} http://localhost:3000/monitoring/status"
    
    echo ""
    log "数据库连接信息:"
    echo -e "  ${BLUE}主机:${NC} localhost:3306"
    echo -e "  ${BLUE}数据库:${NC} ${MYSQL_DATABASE:-health_uplink}"
    echo -e "  ${BLUE}用户名:${NC} ${MYSQL_USER:-health_user}"
}

# 停止服务
stop_services() {
    log "停止服务..."
    docker-compose down
    log "服务已停止"
}

# 重启服务
restart_services() {
    log "重启服务..."
    docker-compose restart
    log "服务已重启"
}

# 查看日志
view_logs() {
    local service=${1:-}
    
    if [ -z "$service" ]; then
        log "查看所有服务日志..."
        docker-compose logs -f
    else
        log "查看 $service 服务日志..."
        docker-compose logs -f "$service"
    fi
}

# 清理数据
cleanup() {
    warn "这将删除所有容器、镜像和数据，确定要继续吗? (y/N)"
    read -r response
    
    if [[ "$response" =~ ^[Yy]$ ]]; then
        log "清理 Docker 资源..."
        docker-compose down -v --rmi all
        docker system prune -f
        log "清理完成"
    else
        log "取消清理操作"
    fi
}

# 显示帮助信息
show_help() {
    echo "Health Uplink Docker 管理脚本"
    echo ""
    echo "使用方法: $0 [命令]"
    echo ""
    echo "命令:"
    echo "  start     启动所有服务"
    echo "  stop      停止所有服务"
    echo "  restart   重启所有服务"
    echo "  status    显示服务状态"
    echo "  logs      查看日志 [服务名]"
    echo "  build     构建镜像"
    echo "  cleanup   清理所有数据"
    echo "  help      显示帮助信息"
    echo ""
    echo "示例:"
    echo "  $0 start"
    echo "  $0 logs backend"
    echo "  $0 status"
}

# 主函数
main() {
    local command=${1:-start}
    
    case "$command" in
        "start")
            check_requirements
            check_env_file
            create_directories
            build_images
            start_services
            show_status
            ;;
        "stop")
            stop_services
            ;;
        "restart")
            restart_services
            ;;
        "status")
            show_status
            ;;
        "logs")
            view_logs "$2"
            ;;
        "build")
            check_requirements
            build_images
            ;;
        "cleanup")
            cleanup
            ;;
        "help"|"-h"|"--help")
            show_help
            ;;
        *)
            error "未知命令: $command"
            show_help
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"

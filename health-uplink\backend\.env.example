# 数据库配置
DB_HOST=localhost
DB_PORT=3306
DB_NAME=health_uplink
DB_USER=your_username
DB_PASSWORD=your_password

# JWT配置
JWT_SECRET=your_jwt_secret_key_here_please_change_in_production

# 服务器配置
PORT=3000
NODE_ENV=development

# 数据库同步配置
# DB_SYNC_ENABLED: 是否启用数据库同步 (true/false)
# 生产环境建议设置为false，手动管理数据库迁移
DB_SYNC_ENABLED=true

# DB_SYNC_FORCE: 强制同步模式，会删除现有表重新创建 (true/false)
# 警告：在生产环境中请务必设置为false，避免数据丢失
# 仅在开发环境且NODE_ENV=development时生效
DB_SYNC_FORCE=false

# DB_SYNC_ALTER: 结构更新模式，修改现有表结构以匹配模型 (true/false)
# 谨慎使用：可能导致数据丢失，建议在测试环境验证后使用
DB_SYNC_ALTER=false

# 文件上传配置
UPLOAD_DIR=upload/images
MAX_FILE_SIZE=10485760  # 10MB

# 日志配置
LOG_LEVEL=info
LOG_DIR=log/backend

# 管理员默认账户（首次启动时创建）
DEFAULT_ADMIN_USERNAME=admin
DEFAULT_ADMIN_PASSWORD=admin123

# API限流配置
RATE_LIMIT_WINDOW_MS=900000  # 15分钟
RATE_LIMIT_MAX_REQUESTS=100  # 最大请求次数

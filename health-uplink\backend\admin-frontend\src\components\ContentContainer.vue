<template>
  <div class="content-container" :class="containerClass">
    <!-- 页面头部 -->
    <div v-if="showHeader" class="standard-page-header">
      <div class="header-left">
        <h1 v-if="title" class="standard-page-title">{{ title }}</h1>
        <p v-if="description" class="standard-page-description">{{ description }}</p>
        <slot name="header-left" />
      </div>
      <div class="header-right">
        <slot name="header-right" />
      </div>
    </div>

    <!-- 面包屑导航 -->
    <div v-if="showBreadcrumb && breadcrumbs.length > 0" class="standard-breadcrumb-container">
      <el-breadcrumb separator="/">
        <el-breadcrumb-item
          v-for="(item, index) in breadcrumbs"
          :key="index"
          :to="item.path"
        >
          {{ item.title }}
        </el-breadcrumb-item>
      </el-breadcrumb>
    </div>

    <!-- 页面内容 -->
    <div class="standard-content-area page-content" :class="contentClass">
      <slot />
    </div>

    <!-- 页面底部 -->
    <div v-if="showFooter" class="page-footer">
      <slot name="footer" />
    </div>
  </div>
</template>

<script>
import { defineComponent } from 'vue'

export default defineComponent({
  name: 'ContentContainer',
  props: {
    // 页面标题
    title: {
      type: String,
      default: ''
    },
    // 页面描述
    description: {
      type: String,
      default: ''
    },
    // 是否显示头部
    showHeader: {
      type: Boolean,
      default: false
    },
    // 是否显示面包屑
    showBreadcrumb: {
      type: Boolean,
      default: false
    },
    // 是否显示底部
    showFooter: {
      type: Boolean,
      default: false
    },
    // 面包屑数据
    breadcrumbs: {
      type: Array,
      default: () => []
    },
    // 容器样式类
    containerClass: {
      type: String,
      default: ''
    },
    // 内容样式类
    contentClass: {
      type: String,
      default: ''
    }
  }
})
</script>

<style scoped>
.content-container {
  background: var(--bg-primary);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-sm);
  margin: var(--spacing-lg);
  overflow: visible; /* 改为visible以支持子元素滚动 */
  transition: var(--transition-normal);
  position: relative;
  z-index: var(--z-content);
}

/* 使用标准化样式，移除重复定义 */

/* page-content 特定样式 */
.page-content {
  padding: var(--spacing-lg);
  min-height: 400px;
}

.page-footer {
  padding: var(--spacing-md) var(--spacing-lg);
  background: var(--bg-tertiary);
  border-top: 1px solid var(--border-secondary);
  text-align: center;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .content-container {
    margin: var(--spacing-sm);
    border-radius: var(--radius-md);
  }

  .page-header {
    flex-direction: column;
    align-items: stretch;
    gap: var(--spacing-md);
  }

  .header-right {
    margin-left: 0;
  }

  .page-content {
    padding: var(--spacing-md);
  }

  .page-title {
    font-size: var(--font-size-xl);
  }
}
</style>
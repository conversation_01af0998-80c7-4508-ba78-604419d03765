/* Health Uplink 设计系统 */

/* ===== CSS 变量定义 ===== */
:root {
  /* 主色调 */
  --primary-color: #1890ff;
  --primary-light: #40a9ff;
  --primary-dark: #096dd9;
  --primary-hover: #40a9ff;
  
  /* 辅助色 */
  --success-color: #52c41a;
  --warning-color: #faad14;
  --error-color: #ff4d4f;
  --info-color: #1890ff;
  
  /* 中性色 */
  --text-primary: #262626;
  --text-secondary: #595959;
  --text-tertiary: #8c8c8c;
  --text-disabled: #bfbfbf;
  
  /* 背景色 */
  --bg-primary: #ffffff;
  --bg-secondary: #fafafa;
  --bg-tertiary: #f5f5f5;
  --bg-disabled: #f5f5f5;
  
  /* 边框色 */
  --border-primary: #d9d9d9;
  --border-secondary: #f0f0f0;
  --border-light: #f5f5f5;
  
  /* 阴影 */
  --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.06);
  --shadow-md: 0 4px 12px rgba(0, 0, 0, 0.1);
  --shadow-lg: 0 8px 24px rgba(0, 0, 0, 0.12);
  
  /* 圆角 */
  --radius-sm: 4px;
  --radius-md: 6px;
  --radius-lg: 8px;
  --radius-xl: 12px;
  
  /* 间距 */
  --spacing-xs: 4px;
  --spacing-sm: 8px;
  --spacing-md: 16px;
  --spacing-lg: 24px;
  --spacing-xl: 32px;
  --spacing-xxl: 48px;
  
  /* 字体大小 */
  --font-size-xs: 12px;
  --font-size-sm: 14px;
  --font-size-md: 16px;
  --font-size-lg: 18px;
  --font-size-xl: 20px;
  --font-size-xxl: 24px;
  
  /* 行高 */
  --line-height-sm: 1.2;
  --line-height-md: 1.5;
  --line-height-lg: 1.8;
  
  /* 字体粗细 */
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;
  
  /* 过渡动画 */
  --transition-fast: 0.15s ease-in-out;
  --transition-normal: 0.3s ease-in-out;
  --transition-slow: 0.5s ease-in-out;
  
  /* Z-index层级 */
  --z-dropdown: 1000;
  --z-modal: 1050;
  --z-popover: 1060;
  --z-tooltip: 1070;
  --z-content: 1;
  --z-list-container: 10;
  --z-grid-container: 20;
}

/* ===== 全局重置样式 ===== */
* {
  box-sizing: border-box;
}

html {
  font-size: 14px;
  line-height: var(--line-height-md);
}

body {
  margin: 0;
  padding: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  font-size: var(--font-size-sm);
  color: var(--text-primary);
  background-color: var(--bg-secondary);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* ===== 通用工具类 ===== */
.text-primary { color: var(--text-primary) !important; }
.text-secondary { color: var(--text-secondary) !important; }
.text-tertiary { color: var(--text-tertiary) !important; }
.text-success { color: var(--success-color) !important; }
.text-warning { color: var(--warning-color) !important; }
.text-error { color: var(--error-color) !important; }

.bg-primary { background-color: var(--bg-primary) !important; }
.bg-secondary { background-color: var(--bg-secondary) !important; }

.font-normal { font-weight: var(--font-weight-normal) !important; }
.font-medium { font-weight: var(--font-weight-medium) !important; }
.font-semibold { font-weight: var(--font-weight-semibold) !important; }
.font-bold { font-weight: var(--font-weight-bold) !important; }

.text-xs { font-size: var(--font-size-xs) !important; }
.text-sm { font-size: var(--font-size-sm) !important; }
.text-md { font-size: var(--font-size-md) !important; }
.text-lg { font-size: var(--font-size-lg) !important; }

/* 间距工具类 */
.m-0 { margin: 0 !important; }
.m-xs { margin: var(--spacing-xs) !important; }
.m-sm { margin: var(--spacing-sm) !important; }
.m-md { margin: var(--spacing-md) !important; }
.m-lg { margin: var(--spacing-lg) !important; }

.mt-0 { margin-top: 0 !important; }
.mt-xs { margin-top: var(--spacing-xs) !important; }
.mt-sm { margin-top: var(--spacing-sm) !important; }
.mt-md { margin-top: var(--spacing-md) !important; }
.mt-lg { margin-top: var(--spacing-lg) !important; }

.mb-0 { margin-bottom: 0 !important; }
.mb-xs { margin-bottom: var(--spacing-xs) !important; }
.mb-sm { margin-bottom: var(--spacing-sm) !important; }
.mb-md { margin-bottom: var(--spacing-md) !important; }
.mb-lg { margin-bottom: var(--spacing-lg) !important; }

.p-0 { padding: 0 !important; }
.p-xs { padding: var(--spacing-xs) !important; }
.p-sm { padding: var(--spacing-sm) !important; }
.p-md { padding: var(--spacing-md) !important; }
.p-lg { padding: var(--spacing-lg) !important; }

/* 布局工具类 */
.flex { display: flex !important; }
.flex-col { flex-direction: column !important; }
.flex-wrap { flex-wrap: wrap !important; }
.items-center { align-items: center !important; }
.justify-center { justify-content: center !important; }
.justify-between { justify-content: space-between !important; }

.w-full { width: 100% !important; }
.h-full { height: 100% !important; }

/* 圆角工具类 */
.rounded-sm { border-radius: var(--radius-sm) !important; }
.rounded-md { border-radius: var(--radius-md) !important; }
.rounded-lg { border-radius: var(--radius-lg) !important; }

/* 阴影工具类 */
.shadow-sm { box-shadow: var(--shadow-sm) !important; }
.shadow-md { box-shadow: var(--shadow-md) !important; }
.shadow-lg { box-shadow: var(--shadow-lg) !important; }

/* ===== 页面布局样式 ===== */
.page-container {
  background: var(--bg-primary);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-sm);
  padding: var(--spacing-lg);
  margin-bottom: var(--spacing-lg);
}

.page-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--spacing-lg);
  padding-bottom: var(--spacing-md);
  border-bottom: 1px solid var(--border-secondary);
}

.page-title {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
  margin: 0;
}

.page-content {
  min-height: 400px;
}

/* ===== 卡片样式 ===== */
.card {
  background: var(--bg-primary);
  border: 1px solid var(--border-secondary);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-sm);
  transition: var(--transition-normal);
}

.card:hover {
  box-shadow: var(--shadow-md);
}

.card-header {
  padding: var(--spacing-md) var(--spacing-lg);
  border-bottom: 1px solid var(--border-secondary);
  background: var(--bg-tertiary);
  border-radius: var(--radius-lg) var(--radius-lg) 0 0;
}

.card-body {
  padding: var(--spacing-lg);
}

.card-footer {
  padding: var(--spacing-md) var(--spacing-lg);
  border-top: 1px solid var(--border-secondary);
  background: var(--bg-tertiary);
  border-radius: 0 0 var(--radius-lg) var(--radius-lg);
}

/* ===== 表格样式增强 ===== */
.table-container {
  background: var(--bg-primary);
  border-radius: var(--radius-lg);
  overflow: hidden;
  box-shadow: var(--shadow-sm);
}

.table-header {
  background: var(--bg-tertiary);
  padding: var(--spacing-md) var(--spacing-lg);
  border-bottom: 1px solid var(--border-secondary);
}

.table-actions {
  display: flex;
  gap: var(--spacing-sm);
  align-items: center;
}

/* ===== 状态样式 ===== */
.status-success {
  color: var(--success-color);
  background: rgba(82, 196, 26, 0.1);
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-sm);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-medium);
}

.status-warning {
  color: var(--warning-color);
  background: rgba(250, 173, 20, 0.1);
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-sm);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-medium);
}

.status-error {
  color: var(--error-color);
  background: rgba(255, 77, 79, 0.1);
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-sm);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-medium);
}

/* ===== 标准化列表控件样式 ===== */

/* 标准面包屑容器 */
.standard-breadcrumb-container {
  padding: var(--spacing-md) var(--spacing-lg);
  background: var(--bg-secondary);
  border-bottom: 1px solid var(--border-light);
}

/* 标准页面头部 */
.standard-page-header {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  padding: var(--spacing-lg) var(--spacing-lg) var(--spacing-md);
  border-bottom: 1px solid var(--border-secondary);
  background: var(--bg-tertiary);
}

.standard-page-header .header-left {
  flex: 1;
}

.standard-page-header .header-right {
  flex-shrink: 0;
  margin-left: var(--spacing-lg);
}

.standard-page-title {
  font-size: var(--font-size-xxl);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
  margin: 0 0 var(--spacing-xs) 0;
  line-height: var(--line-height-sm);
}

.standard-page-description {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  margin: 0;
  line-height: var(--line-height-md);
}

/* 标准列表容器 */
.standard-list-container {
  background: var(--bg-primary);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-sm);
  overflow: hidden;
  position: relative;
  z-index: var(--z-list-container);
}

/* 标准内容区域 - 支持滚动 */
.standard-content-area {
  position: relative;
  z-index: var(--z-content);
  overflow: visible; /* 允许子元素溢出 */
}

/* 标准滚动容器 */
.standard-scroll-container {
  max-height: calc(100vh - 300px);
  overflow-y: auto;
  overflow-x: hidden;
  padding-bottom: var(--spacing-lg);
  position: relative;
  z-index: var(--z-grid-container);
}

/* 标准网格布局 */
.standard-grid-layout {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: var(--spacing-lg);
  padding: var(--spacing-lg);
  position: relative;
  z-index: var(--z-grid-container);
}

/* 标准表格容器 */
.standard-table-container {
  position: relative;
  z-index: var(--z-list-container);
  overflow: visible;
}

/* 标准工具栏 */
.standard-toolbar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-md) var(--spacing-lg);
  background: var(--bg-tertiary);
  border-bottom: 1px solid var(--border-secondary);
}

.standard-toolbar-title {
  font-size: var(--font-size-md);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
}

.standard-toolbar-actions {
  display: flex;
  gap: var(--spacing-sm);
  align-items: center;
}

/* 标准分页容器 */
.standard-pagination-container {
  padding: var(--spacing-md) var(--spacing-lg);
  background: var(--bg-tertiary);
  border-top: 1px solid var(--border-secondary);
  display: flex;
  justify-content: flex-end;
}

/* 响应式断点 */
@media (max-width: 1200px) {
  .standard-grid-layout {
    grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
  }

  .standard-scroll-container {
    max-height: calc(100vh - 320px);
  }
}

@media (max-width: 768px) {
  .standard-grid-layout {
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: var(--spacing-md);
    padding: var(--spacing-md);
  }

  .standard-page-header {
    flex-direction: column;
    gap: var(--spacing-md);
  }

  .standard-page-header .header-right {
    margin-left: 0;
    align-self: stretch;
  }

  .standard-toolbar {
    flex-direction: column;
    gap: var(--spacing-md);
    align-items: stretch;
  }

  .standard-scroll-container {
    max-height: calc(100vh - 350px);
  }
}

@media (max-width: 480px) {
  .standard-grid-layout {
    grid-template-columns: 1fr;
  }

  .standard-scroll-container {
    max-height: calc(100vh - 380px);
  }
}

/* pages/patient/patient.wxss */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.container {
  padding-left: 20px;
  padding-right: 20px;
  padding-bottom: 20px;
  background-color: #f8f8f8;
  min-height: 100vh;
  animation: fadeIn 0.5s ease-out; /* 应用淡入动画 */
}

.form-item {
  background-color: #fff;
  border-radius: 8px;
  padding: 15px;
  margin-bottom: 15px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.form-item .label {
  display: block;
  font-size: 16px;
  color: #333;
  margin-bottom: 8px;
  font-weight: bold;
}

.form-item input,
.form-item textarea {
  width: 100%;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  padding: 10px;
  box-sizing: border-box;
  font-size: 15px;
  color: #555;
  vertical-align: middle; /* 垂直居中对齐 */
  min-height: 36px; /* 确保输入框有最小高度 */
}

.form-item input:focus,
.form-item textarea:focus {
  border-color: #1890ff;
  outline: none;
}

.form-item textarea {
  min-height: 80px;
  resize: vertical;
}

.input-error {
  border-color: #ff4d4f !important;
}

.error-message {
  color: #ff4d4f;
  font-size: 12px;
  margin-top: 5px;
  display: block;
}

.submit-button {
  width: 100%;
  background-color: #1890ff;
  color: #fff;
  border-radius: 8px;
  padding: 12px 0;
  font-size: 18px;
  margin-top: 20px;
  box-shadow: 0 4px 10px rgba(24, 144, 255, 0.3);
  transition: transform 0.2s ease-in-out; /* 添加过渡效果 */
}

.submit-button:active { /* 添加点击反馈样式 */
  transform: translateY(2px);
  box-shadow: 0 2px 5px rgba(24, 144, 255, 0.5);
}

.submit-button::after {
  border: none;
}

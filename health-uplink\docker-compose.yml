version: '3.8'

services:
  # MySQL数据库
  mysql:
    image: mysql:8.0
    container_name: health-uplink-mysql
    restart: unless-stopped
    environment:
      MYSQL_ROOT_PASSWORD: ${MYSQL_ROOT_PASSWORD:-rootpassword}
      MYSQL_DATABASE: ${MYSQL_DATABASE:-health_uplink}
      MYSQL_USER: ${MYSQL_USER:-health_user}
      MYSQL_PASSWORD: ${MYSQL_PASSWORD:-health_password}
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./mysql/init:/docker-entrypoint-initdb.d
      - ./mysql/conf:/etc/mysql/conf.d
    networks:
      - health-uplink-network
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost"]
      timeout: 20s
      retries: 10

  # Redis缓存（可选）
  redis:
    image: redis:7-alpine
    container_name: health-uplink-redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - health-uplink-network
    command: redis-server --appendonly yes

  # 后端服务
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: health-uplink-backend
    restart: unless-stopped
    environment:
      NODE_ENV: production
      DB_HOST: mysql
      DB_PORT: 3306
      DB_NAME: ${MYSQL_DATABASE:-health_uplink}
      DB_USER: ${MYSQL_USER:-health_user}
      DB_PASSWORD: ${MYSQL_PASSWORD:-health_password}
      JWT_SECRET: ${JWT_SECRET:-your_jwt_secret_change_in_production}
      PORT: 3000
      REDIS_URL: redis://redis:6379
      # 数据库同步配置（生产环境建议禁用）
      DB_SYNC_ENABLED: ${DB_SYNC_ENABLED:-false}
      DB_SYNC_FORCE: ${DB_SYNC_FORCE:-false}
      DB_SYNC_ALTER: ${DB_SYNC_ALTER:-false}
    ports:
      - "3000:3000"
    volumes:
      - ./backend/uploads:/app/uploads
      - ./backend/logs:/app/logs
    depends_on:
      mysql:
        condition: service_healthy
      redis:
        condition: service_started
    networks:
      - health-uplink-network
    healthcheck:
      test: ["CMD", "node", "healthcheck.js"]
      interval: 30s
      timeout: 10s
      retries: 3

  # 管理后台
  admin-frontend:
    build:
      context: ./backend/admin-frontend
      dockerfile: Dockerfile
    container_name: health-uplink-admin
    restart: unless-stopped
    ports:
      - "80:80"
    depends_on:
      - backend
    networks:
      - health-uplink-network

  # 数据库备份服务
  backup:
    image: mysql:8.0
    container_name: health-uplink-backup
    restart: "no"
    environment:
      MYSQL_HOST: mysql
      MYSQL_DATABASE: ${MYSQL_DATABASE:-health_uplink}
      MYSQL_USER: ${MYSQL_USER:-health_user}
      MYSQL_PASSWORD: ${MYSQL_PASSWORD:-health_password}
    volumes:
      - ./backups:/backups
      - ./scripts:/scripts
    depends_on:
      mysql:
        condition: service_healthy
    networks:
      - health-uplink-network
    command: /scripts/backup.sh

volumes:
  mysql_data:
    driver: local
  redis_data:
    driver: local

networks:
  health-uplink-network:
    driver: bridge

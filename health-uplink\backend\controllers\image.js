const { Image, Patient } = require('../models');
const upload = require('../middlewares/upload');
const multer = require('multer'); // 添加引入 multer 模块
const fs = require('fs');
const path = require('path');

// 上传图片
async function uploadImage(req, res) {
  console.log('=== 图片上传开始 ===');
  console.log('请求方法:', req.method);
  console.log('请求URL:', req.url);
  console.log('Content-Type:', req.headers['content-type']);
  console.log('请求体:', req.body);
  console.log('上传文件数量:', req.files ? req.files.length : 0);

  if (req.files && req.files.length > 0) {
    req.files.forEach((file, index) => {
      console.log(`文件 ${index + 1}:`, {
        fieldname: file.fieldname,
        originalname: file.originalname,
        filename: file.filename,
        size: file.size,
        mimetype: file.mimetype
      });
    });
  }

  try {
    if (!req.files || req.files.length === 0) {
      console.log('没有接收到文件');
      return res.status(400).json({ message: '请选择要上传的图片。' });
    }

    const { patient_id, image_type } = req.body;
    console.log('解析的参数:', { patient_id, image_type });

    // 简化逻辑：暂时不验证患者ID，直接创建图片记录
    const uploadedImages = [];
    for (const file of req.files) {
      const imageUrl = `/upload/images/${file.filename}`;
      console.log('创建图片记录:', {
        filename: file.filename,
        original_name: file.originalname,
        image_url: imageUrl,
        image_type: image_type || '其他'
      });

      const image = await Image.create({
        filename: file.filename,
        original_name: file.originalname,
        file_path: file.path,
        file_size: file.size,
        mime_type: file.mimetype,
        patient_id: patient_id || null,
        image_url: imageUrl,
        image_type: image_type || '其他'
      });
      uploadedImages.push(image);
    }

    console.log('图片上传成功，返回结果');
    res.status(201).json({
      message: '图片上传成功',
      images: uploadedImages,
      count: uploadedImages.length
    });
  } catch (error) {
    console.error('图片上传失败:', error);
    // 清理已上传的文件
    if (req.files) {
      req.files.forEach(file => {
        try {
          if (fs.existsSync(file.path)) {
            fs.unlinkSync(file.path);
            console.log('已删除文件:', file.path);
          }
        } catch (unlinkError) {
          console.error('删除文件失败:', unlinkError);
        }
      });
    }
    res.status(500).json({ message: '图片上传失败', error: error.message });
  }
}

// 获取图片列表
async function getImageList(req, res) {
  try {
    const { patient_id, page = 1, limit = 10 } = req.query;
    const offset = (page - 1) * limit;

    const whereClause = {};
    if (patient_id) {
      whereClause.patient_id = patient_id;
    }

    const { count, rows: images } = await Image.findAndCountAll({
      where: whereClause,
      limit: parseInt(limit),
      offset: parseInt(offset),
      order: [['created_at', 'DESC']]
    });

    res.status(200).json({
      total: count,
      page: parseInt(page),
      limit: parseInt(limit),
      images
    });
  } catch (error) {
    console.error('获取图片列表失败:', error);
    res.status(500).json({ message: '获取图片列表失败', error: error.message });
  }
}

// 获取图片详情
async function getImageDetail(req, res) {
  try {
    const { id } = req.params;
    const image = await Image.findByPk(id, {
      include: [{
        model: Patient,
        as: 'patient'
      }]
    });

    if (!image) {
      return res.status(404).json({ message: '未找到该图片。' });
    }

    res.status(200).json({ image });
  } catch (error) {
    console.error('获取图片详情失败:', error);
    res.status(500).json({ message: '获取图片详情失败', error: error.message });
  }
}

// 删除图片
async function deleteImage(req, res) {
  try {
    const { id } = req.params;
    const image = await Image.findByPk(id);

    if (!image) {
      return res.status(404).json({ success: false, message: '未找到该图片。' });
    }

    // 从文件系统删除图片
    // image.image_url 格式为 "/upload/images/filename"，需要转换为实际文件路径
    let imagePath;
    if (image.file_path && fs.existsSync(image.file_path)) {
      // 优先使用 file_path 字段（完整路径）
      imagePath = image.file_path;
    } else if (image.image_url) {
      // 如果没有 file_path，从 image_url 构建路径
      // 移除开头的 "/" 并构建完整路径
      const relativePath = image.image_url.startsWith('/') ? image.image_url.substring(1) : image.image_url;
      imagePath = path.join(__dirname, '..', relativePath);
    }

    // 删除文件系统中的图片文件
    if (imagePath && fs.existsSync(imagePath)) {
      try {
        fs.unlinkSync(imagePath);
        console.log('已删除图片文件:', imagePath);
      } catch (fileError) {
        console.warn('删除图片文件失败:', fileError.message);
        // 即使文件删除失败，也继续删除数据库记录
      }
    } else {
      console.warn('图片文件不存在或路径无效:', imagePath);
    }

    // 删除数据库记录
    await image.destroy();

    res.status(200).json({ success: true, message: '图片删除成功。' });
  } catch (error) {
    console.error('删除图片失败:', error);
    res.status(500).json({ success: false, message: '删除图片失败', error: error.message });
  }
}

module.exports = {
  uploadImage,
  getImageList,
  getImageDetail,
  deleteImage
};
@echo off
echo ========================================
echo Health Uplink 开发环境启动脚本
echo ========================================
echo.

echo 正在启动后端服务...
start "Health Uplink Backend" cmd /k "cd /d %~dp0backend && node app.js"

echo 等待后端服务启动...
timeout /t 3 /nobreak >nul

echo 正在启动前端开发服务器...
start "Health Uplink Frontend" cmd /k "cd /d %~dp0backend\admin-frontend && npm run dev"

echo.
echo ========================================
echo 服务启动完成！
echo ========================================
echo.
echo 前端地址: http://localhost:5173/
echo 后端地址: http://localhost:3000/
echo.
echo 主要页面:
echo - 系统状态: http://localhost:5173/status
echo - 组件展示: http://localhost:5173/components
echo - 患者管理: http://localhost:5173/patients
echo - 图片管理: http://localhost:5173/images
echo.
echo 监控端点:
echo - 健康检查: http://localhost:3000/health
echo - 系统状态: http://localhost:3000/monitoring/status
echo - Prometheus指标: http://localhost:3000/monitoring/metrics
echo.
echo 按任意键打开前端页面...
pause >nul
start http://localhost:5173/

const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');
const { Doctor } = require('../models'); // 引入 Doctor 模型

// 登录接口
async function login(req, res) {
  try {
    const { username, password } = req.body;
    console.info('登录请求 - 用户名:', username);
    console.info('登录请求 - 密码:', password);
    // 简单验证输入
    if (!username || !password) {
      return res.status(400).json({ message: '用户名和密码是必填项。' });
    }

    // 查找医生用户
    const doctor = await Doctor.findOne({ where: { name: username } });
    if (!doctor) {
      return res.status(401).json({ message: '用户名或密码不正确。' });
    }

    // 验证密码
    const isMatch = await bcrypt.compare(password, doctor.password);
    if (!isMatch) {
      return res.status(401).json({ message: '用户名或密码不正确。' });
    }

    // 生成 JWT Token
    const jwtSecret = process.env.JWT_SECRET || 'your_jwt_secret_key_here_please_change_in_production';
    console.log('登录时使用的 JWT_SECRET:', jwtSecret);
    const token = jwt.sign(
      { id: doctor.doctor_id, name: doctor.name },
      jwtSecret, // 使用环境变量或默认密钥
      { expiresIn: '1h' } // Token 有效期 1 小时
    );

    res.status(200).json({ message: '登录成功', token, doctor: { id: doctor.doctor_id, name: doctor.name } });
  } catch (error) {
    console.error('登录失败:', error);
    res.status(500).json({ message: '登录失败', error: error.message });
  }
}

module.exports = {
  login
};

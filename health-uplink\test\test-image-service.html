<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>图片服务测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .result {
            background: #f5f5f5;
            padding: 10px;
            margin: 10px 0;
            border-radius: 3px;
        }
        .error {
            background: #ffe6e6;
            color: #d00;
        }
        .success {
            background: #e6ffe6;
            color: #060;
        }
        button {
            padding: 10px 20px;
            margin: 5px;
            background: #007cba;
            color: white;
            border: none;
            border-radius: 3px;
            cursor: pointer;
        }
        button:hover {
            background: #005a87;
        }
        img {
            max-width: 200px;
            max-height: 200px;
            margin: 10px;
            border: 1px solid #ddd;
        }
    </style>
</head>
<body>
    <h1>图片服务测试页面</h1>
    
    <div class="test-section">
        <h2>1. 测试后端连接</h2>
        <button onclick="testBackend()">测试后端健康检查</button>
        <div id="backend-result" class="result"></div>
    </div>

    <div class="test-section">
        <h2>2. 测试调试API</h2>
        <button onclick="testDebugAPI()">测试调试API</button>
        <div id="debug-result" class="result"></div>
    </div>

    <div class="test-section">
        <h2>3. 测试图片列表API</h2>
        <button onclick="testImageListAPI()">测试图片列表API</button>
        <div id="imagelist-result" class="result"></div>
    </div>

    <div class="test-section">
        <h2>4. 测试图片访问</h2>
        <button onclick="testImageAccess()">测试图片访问</button>
        <div id="image-access-result" class="result"></div>
        <div id="image-display"></div>
    </div>

    <script>
        const baseUrl = 'http://localhost:3000';

        async function testBackend() {
            const resultDiv = document.getElementById('backend-result');
            try {
                const response = await fetch(`${baseUrl}/health`);
                const data = await response.json();
                resultDiv.className = 'result success';
                resultDiv.innerHTML = `✅ 后端连接成功<br><pre>${JSON.stringify(data, null, 2)}</pre>`;
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `❌ 后端连接失败: ${error.message}`;
            }
        }

        async function testDebugAPI() {
            const resultDiv = document.getElementById('debug-result');
            try {
                const response = await fetch(`${baseUrl}/api/debug/images`);
                const data = await response.json();
                resultDiv.className = 'result success';
                resultDiv.innerHTML = `✅ 调试API成功<br><pre>${JSON.stringify(data, null, 2)}</pre>`;
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `❌ 调试API失败: ${error.message}`;
            }
        }

        async function testImageListAPI() {
            const resultDiv = document.getElementById('imagelist-result');
            try {
                const response = await fetch(`${baseUrl}/api/image/list`);
                const data = await response.json();
                resultDiv.className = 'result success';
                resultDiv.innerHTML = `✅ 图片列表API成功<br>找到 ${data.images ? data.images.length : 0} 张图片<br><pre>${JSON.stringify(data, null, 2)}</pre>`;
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `❌ 图片列表API失败: ${error.message}`;
            }
        }

        async function testImageAccess() {
            const resultDiv = document.getElementById('image-access-result');
            const displayDiv = document.getElementById('image-display');
            
            try {
                // 首先获取调试信息
                const debugResponse = await fetch(`${baseUrl}/api/debug/images`);
                const debugData = await debugResponse.json();
                
                if (debugData.files && debugData.files.length > 0) {
                    const testImageName = debugData.files[0];
                    const imageUrl = `${baseUrl}/upload/images/${testImageName}`;
                    
                    // 测试图片访问
                    const img = new Image();
                    img.onload = () => {
                        resultDiv.className = 'result success';
                        resultDiv.innerHTML = `✅ 图片访问成功: ${imageUrl}`;
                        displayDiv.innerHTML = `<img src="${imageUrl}" alt="测试图片">`;
                    };
                    img.onerror = () => {
                        resultDiv.className = 'result error';
                        resultDiv.innerHTML = `❌ 图片访问失败: ${imageUrl}`;
                        displayDiv.innerHTML = '';
                    };
                    img.src = imageUrl;
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.innerHTML = '❌ 没有找到可测试的图片文件';
                    displayDiv.innerHTML = '';
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `❌ 图片访问测试失败: ${error.message}`;
                displayDiv.innerHTML = '';
            }
        }
    </script>
</body>
</html>

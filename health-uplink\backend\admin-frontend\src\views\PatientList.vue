<template>
  <ContentContainer
    title="患者管理"
    description="管理和查看患者信息，包括基本信息、病史和症状记录"
    :show-header="true"
    :breadcrumbs="breadcrumbs"
    :show-breadcrumb="true"
  >
    <!-- 搜索表单 -->
    <SearchForm
      :search-fields="searchFields"
      :loading="loading"
      @search="handleSearch"
      @reset="handleReset"
    />

    <!-- 数据表格 -->
    <div class="standard-list-container">
      <div class="standard-content-area">
        <div class="standard-table-container">
          <DataTable
            :data="patients"
            :columns="tableColumns"
            :loading="loading"
            :pagination="pagination"
            title="患者列表"
            :max-height="600"
            @current-change="handleCurrentChange"
            @size-change="handleSizeChange"
            @view="handleView"
            @edit="handleEdit"
            @delete="handleDelete"
            @refresh="fetchPatients"
          >
      <!-- 自定义操作列 -->
      <template #actions="{ row }">
        <el-button
          link
          size="small"
          @click="viewPatientDetail(row.patient_id)"
          icon="View"
        >
          详情
        </el-button>
        <el-button
          link
          size="small"
          @click="handleEdit(row)"
          icon="Edit"
        >
          编辑
        </el-button>
        <el-button
          link
          size="small"
          @click="handleDelete(row)"
          class="text-error"
          icon="Delete"
        >
          删除
        </el-button>
      </template>
          </DataTable>
        </div>
      </div>
    </div>
  </ContentContainer>
</template>

<script>
import { defineComponent, ref, onMounted, computed } from 'vue';
import { useRouter } from 'vue-router';
import { ElMessage, ElMessageBox } from 'element-plus';
import api from '../api';
import ContentContainer from '../components/ContentContainer.vue';
import SearchForm from '../components/SearchForm.vue';
import DataTable from '../components/DataTable.vue';

export default defineComponent({
  components: {
    ContentContainer,
    SearchForm,
    DataTable
  },
  setup() {
    const router = useRouter();
    const patients = ref([]);
    const loading = ref(false);
    const searchParams = ref({});

    // 分页配置
    const pagination = ref({
      page: 1,
      pageSize: 10,
      total: 0
    });

    // 面包屑导航
    const breadcrumbs = computed(() => [
      { title: '首页', path: '/' },
      { title: '患者管理', path: '/patients' }
    ]);

    // 搜索字段配置
    const searchFields = computed(() => [
      {
        key: 'search',
        label: '关键词',
        type: 'input',
        placeholder: '请输入姓名、病史或症状',
        clearable: true
      },
      {
        key: 'age_min',
        label: '最小年龄',
        type: 'input',
        placeholder: '最小年龄',
        clearable: true
      },
      {
        key: 'age_max',
        label: '最大年龄',
        type: 'input',
        placeholder: '最大年龄',
        clearable: true
      },
      {
        key: 'date_range',
        label: '创建时间',
        type: 'daterange',
        placeholder: '选择时间范围',
        clearable: true
      }
    ]);

    // 表格列配置
    const tableColumns = computed(() => [
      {
        prop: 'patient_id',
        label: 'ID',
        width: 80,
        align: 'center'
      },
      {
        prop: 'name',
        label: '姓名',
        minWidth: 100,
        showOverflowTooltip: true
      },
      {
        prop: 'age',
        label: '年龄',
        width: 80,
        align: 'center'
      },
      {
        prop: 'medical_history',
        label: '病史',
        minWidth: 150,
        showOverflowTooltip: true,
        formatter: (row) => row.medical_history || '无'
      },
      {
        prop: 'symptoms',
        label: '症状',
        minWidth: 150,
        showOverflowTooltip: true,
        formatter: (row) => row.symptoms || '无'
      },
      {
        prop: 'created_at',
        label: '创建时间',
        width: 180,
        align: 'center',
        formatter: (row) => {
          if (!row.created_at) return '-';
          return new Date(row.created_at).toLocaleString('zh-CN');
        }
      }
    ]);

    // 获取患者列表
    const fetchPatients = async () => {
      loading.value = true;
      try {
        const params = {
          page: pagination.value.page,
          limit: pagination.value.pageSize,
          ...searchParams.value
        };

        const response = await api.get('/patient/list', { params });

        if (response.patients) {
          patients.value = response.patients;
          pagination.value.total = response.total;
        } else {
          ElMessage.error(response.message || '获取患者列表失败');
        }
      } catch (error) {
        console.error('获取患者列表失败:', error);
        ElMessage.error('获取患者列表失败，请稍后重试');
      } finally {
        loading.value = false;
      }
    };

    // 搜索处理
    const handleSearch = (searchData) => {
      searchParams.value = searchData;
      pagination.value.page = 1; // 重置到第一页
      fetchPatients();
    };

    // 重置搜索
    const handleReset = () => {
      searchParams.value = {};
      pagination.value.page = 1;
      fetchPatients();
    };

    // 分页处理
    const handleSizeChange = (newSize) => {
      pagination.value.pageSize = newSize;
      pagination.value.page = 1;
      fetchPatients();
    };

    const handleCurrentChange = (newPage) => {
      pagination.value.page = newPage;
      fetchPatients();
    };

    // 操作处理
    const viewPatientDetail = (patientId) => {
      router.push(`/patient/${patientId}`);
    };

    const handleView = (row) => {
      viewPatientDetail(row.patient_id);
    };

    const handleEdit = (row) => {
      router.push(`/patient/${row.patient_id}/edit`);
    };

    const handleDelete = async (row) => {
      try {
        await ElMessageBox.confirm(
          `确定要删除患者 "${row.name}" 的信息吗？此操作不可恢复。`,
          '删除确认',
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
          }
        );

        const response = await api.delete(`/patient/${row.patient_id}`);

        if (response.message) {
          ElMessage.success('患者信息删除成功');
          fetchPatients(); // 刷新列表
        } else {
          ElMessage.error('删除失败');
        }
      } catch (error) {
        if (error !== 'cancel') {
          console.error('删除患者失败:', error);
          ElMessage.error('删除失败，请稍后重试');
        }
      }
    };

    onMounted(() => {
      fetchPatients();
    });

    return {
      patients,
      loading,
      pagination,
      breadcrumbs,
      searchFields,
      tableColumns,
      fetchPatients,
      handleSearch,
      handleReset,
      handleSizeChange,
      handleCurrentChange,
      viewPatientDetail,
      handleView,
      handleEdit,
      handleDelete
    };
  }
});
</script>

<style scoped>
/* 页面特定样式 */
.text-error {
  color: var(--error-color) !important;
}

/* 响应式调整 */
@media (max-width: 768px) {
  :deep(.el-button) {
    margin-bottom: var(--spacing-xs);
  }
}
</style>
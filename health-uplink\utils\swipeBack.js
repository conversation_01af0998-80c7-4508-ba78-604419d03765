// health-uplink/utils/swipeBack.js

/**
 * 页面右滑返回功能
 * 在页面的js文件中引入此方法，并在页面的生命周期或事件处理函数中调用
 * 例如：
 * import { initSwipeBack } from '../../utils/swipeBack';
 *
 * Page({
 *   onLoad() {
 *     initSwipeBack(this);
 *   },
 *   // ... 其他页面逻辑
 * });
 *
 * @param {Object} pageContext - 当前页面的this上下文
 */
export function initSwipeBack(pageContext) {
  let startX = 0; // 记录触摸开始时的X坐标
  let startY = 0; // 记录触摸开始时的Y坐标
  const threshold = 50; // 触发返回的最小滑动距离阈值 (单位: px)

  pageContext.onTouchStart = function(e) {
    if (e.touches.length === 1) {
      startX = e.touches[0].clientX;
      startY = e.touches[0].clientY;
    }
  };

  pageContext.onTouchMove = function(e) {
    // 可选：在滑动过程中提供视觉反馈
  };

  pageContext.onTouchEnd = function(e) {
    if (e.changedTouches.length === 1) {
      const endX = e.changedTouches[0].clientX;
      const endY = e.changedTouches[0].clientY;
      const deltaX = endX - startX;
      const deltaY = endY - startY;

      // 判断是否为有效的水平滑动（忽略垂直方向的小范围移动）且滑动距离达到阈值
      if (Math.abs(deltaX) > threshold && Math.abs(deltaX) > Math.abs(deltaY)) {
        // 执行返回操作
        wx.navigateBack({
          delta: 1 // 返回上一页
        });
      }
    }
  };

  // 将触摸事件绑定到页面实例上
  // 注意：这种方式需要页面WXML中没有覆盖onTouchStart, onTouchMove, onTouchEnd的元素
  // 或者在页面的WXML根元素上绑定这些事件，并确保事件能冒泡到页面
  // 更推荐的方式是在页面的WXML根元素上绑定事件，例如：
  // <view bindtouchstart="onTouchStart" bindtouchmove="onTouchMove" bindtouchend="onTouchEnd">
  //   <!-- 页面内容 -->
  // </view>
  // 为了简化，这里直接绑定到页面实例，但请注意WXML中的事件绑定优先级更高
  // 如果WXML中已经绑定了这些事件，请在WXML中调用pageContext.onTouchStart等方法
}
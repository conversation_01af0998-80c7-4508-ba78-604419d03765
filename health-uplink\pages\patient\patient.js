// pages/patient/patient.js
const api = require('../../utils/api.js');
import { initSwipeBack } from '../../utils/swipeBack';

Page({

  /**
   * 页面的初始数据
   */
  data: {
    patientInfo: {
      name: '',
      age: null,
      medical_history: '',
      symptoms: ''
    },
    errors: {},
    isLoading: false, // 添加加载状态
    statusBarHeight: 0,
    navigationBarHeight: 44
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    // 尝试从本地缓存加载数据
    console.log('onLoad: Attempting to load patientInfo from storage');
    const savedPatientInfo = wx.getStorageSync('patientInfo');
    if (savedPatientInfo) {
      console.log('onLoad: Found saved patientInfo', savedPatientInfo);
      this.setData({
        patientInfo: savedPatientInfo
      });
    } else {
      console.log('onLoad: No saved patientInfo found');
    }

    // 获取系统信息，计算导航栏高度
    const { statusBarHeight, safeArea } = wx.getSystemInfoSync();
    const navigationBarHeight = (statusBarHeight || 0) + 44; // 44px is the default navigation bar height
    this.setData({
      statusBarHeight,
      navigationBarHeight,
      // 确保内容不会被导航栏遮挡
      contentPaddingTop: navigationBarHeight
    });

    // 初始化右滑返回功能
    initSwipeBack(this);
  },

 /**
  * 生命周期函数--监听页面初次渲染完成
  */
 onReady: function () {

 },

 /**
  * 生命周期函数--监听页面显示
  */
 onShow: function () {
   console.log('Patient page onShow');
   // 可以在这里添加逻辑，例如检查是否需要刷新数据
 },

 /**
  * 生命周期函数--监听页面隐藏
  */
 onHide: function () {
   console.log('onHide: Saving patientInfo to storage', this.data.patientInfo);
   // 页面隐藏时保存数据到本地缓存
   wx.setStorageSync('patientInfo', this.data.patientInfo);
 },

 /**
  * 生命周期函数--监听页面卸载
  */
 onUnload: function () {

 },

 /**
  * 页面相关事件处理函数--监听用户下拉动作
  */
 onPullDownRefresh: function () {

 },

 /**
  * 页面上拉触底事件的处理函数
  */
 onReachBottom: function () {

 },

 /**
  * 用户点击右上角分享
  */
 onShareAppMessage: function () {

 },

 // 表单输入事件
 onInput: function (e) {
   const { field } = e.currentTarget.dataset;
   let value = e.detail.value;

   // 特殊处理年龄字段，确保其为数字类型或 null
   if (field === 'age') {
     const parsedAge = parseInt(value, 10);
     // 如果输入为空字符串或非有效数字，则设置为 null
     value = isNaN(parsedAge) || value.trim() === '' ? null : parsedAge;
   }

   this.setData({
     [`patientInfo.${field}`]: value
   });

   // 输入时清除对应字段的错误提示
   if (this.data.errors[field]) {
     this.setData({
       [`errors.${field}`]: null
     });
   }
 },

 validateForm: function (formData) {
   let errors = {};
   if (!formData.name || formData.name.trim() === '') {
     errors.name = '姓名不能为空';
   }
   // 校验年龄是否为数字且大于0
   if (formData.age === null || formData.age === '' || !/^\d+$/.test(formData.age) || parseInt(formData.age) <= 0) {
      errors.age = '年龄必须是有效数字且大于0';
   }
   // 可以根据需要添加其他字段的校验

   this.setData({
     errors: errors
   });
   return Object.keys(errors).length === 0;
 },

 submitForm: function () {
   const formData = this.data.patientInfo;

   if (!this.validateForm(formData)) {
     api.showErrorToast('请检查填写内容'); // 使用统一的错误提示
     return;
   }

   this.setData({ isLoading: true }); // 显示加载状态

   api.submitPatientInfo(formData)
     .then(res => {
       wx.showToast({
         title: '提交成功',
         icon: 'success',
         duration: 2000
       });
       console.log('submitForm: Patient info submitted successfully. Clearing storage and resetting form.');
       // 提交成功后清空本地缓存和表单数据
       wx.removeStorageSync('patientInfo');
       this.setData({
         patientInfo: {
           name: '',
           age: null,
           medical_history: '',
           symptoms: ''
         },
         errors: {}
       });
     })
     .catch(err => {
       // API层已经处理了错误提示，这里可以根据需要添加额外逻辑
       console.error('提交患者信息失败', err);
     })
     .finally(() => {
       this.setData({ isLoading: false }); // 隐藏加载状态
     });
   }
})

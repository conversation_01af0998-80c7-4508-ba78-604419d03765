# Docker环境配置文件
# 复制此文件为.env并根据实际情况修改配置

# MySQL数据库配置
MYSQL_ROOT_PASSWORD=your_strong_root_password_here
MYSQL_DATABASE=health_uplink
MYSQL_USER=health_user
MYSQL_PASSWORD=your_strong_password_here

# JWT密钥（生产环境请使用强密钥）
JWT_SECRET=your_very_strong_jwt_secret_key_here_change_in_production

# 应用配置
NODE_ENV=production
API_BASE_URL=http://localhost:3000

# 数据库同步配置（生产环境）
# 生产环境建议禁用自动同步，手动管理数据库迁移
DB_SYNC_ENABLED=false
DB_SYNC_FORCE=false
DB_SYNC_ALTER=false

# 备份配置
BACKUP_RETENTION_DAYS=30
BACKUP_SCHEDULE="0 2 * * *"  # 每天凌晨2点备份

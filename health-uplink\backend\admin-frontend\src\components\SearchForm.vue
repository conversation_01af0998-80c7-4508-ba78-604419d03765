<template>
  <div class="search-form-container">
    <el-form 
      :inline="true" 
      :model="searchModel" 
      class="search-form"
      @submit.prevent="handleSearch"
    >
      <!-- 动态渲染搜索字段 -->
      <el-form-item 
        v-for="field in searchFields" 
        :key="field.key"
        :label="field.label"
        :prop="field.key"
      >
        <!-- 输入框 -->
        <el-input
          v-if="field.type === 'input'"
          v-model="searchModel[field.key]"
          :placeholder="field.placeholder"
          :clearable="field.clearable !== false"
          style="width: 200px"
        />
        
        <!-- 选择器 -->
        <el-select
          v-else-if="field.type === 'select'"
          v-model="searchModel[field.key]"
          :placeholder="field.placeholder"
          :clearable="field.clearable !== false"
          style="width: 200px"
        >
          <el-option
            v-for="option in field.options"
            :key="option.value"
            :label="option.label"
            :value="option.value"
          />
        </el-select>
        
        <!-- 日期选择器 -->
        <el-date-picker
          v-else-if="field.type === 'date'"
          v-model="searchModel[field.key]"
          type="date"
          :placeholder="field.placeholder"
          :clearable="field.clearable !== false"
          style="width: 200px"
        />
        
        <!-- 日期范围选择器 -->
        <el-date-picker
          v-else-if="field.type === 'daterange'"
          v-model="searchModel[field.key]"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :clearable="field.clearable !== false"
          style="width: 240px"
        />
      </el-form-item>
      
      <!-- 操作按钮 -->
      <el-form-item class="search-actions">
        <el-button 
          type="primary" 
          @click="handleSearch"
          :loading="loading"
          icon="Search"
        >
          查询
        </el-button>
        <el-button 
          @click="handleReset"
          icon="Refresh"
        >
          重置
        </el-button>
        <el-button 
          v-if="showAdvanced"
          type="text" 
          @click="toggleAdvanced"
          class="advanced-toggle"
        >
          {{ advancedVisible ? '收起' : '展开' }}
          <el-icon class="advanced-icon" :class="{ 'is-expanded': advancedVisible }">
            <ArrowDown />
          </el-icon>
        </el-button>
      </el-form-item>
    </el-form>
    
    <!-- 高级搜索区域 -->
    <div v-if="showAdvanced && advancedVisible" class="advanced-search">
      <el-form :inline="true" :model="searchModel">
        <el-form-item 
          v-for="field in advancedFields" 
          :key="field.key"
          :label="field.label"
          :prop="field.key"
        >
          <!-- 这里可以复用上面的字段渲染逻辑 -->
          <el-input
            v-if="field.type === 'input'"
            v-model="searchModel[field.key]"
            :placeholder="field.placeholder"
            :clearable="field.clearable !== false"
            style="width: 200px"
          />
          <!-- 其他类型字段... -->
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>

<script>
import { defineComponent, ref, reactive, watch } from 'vue'
import { Search, Refresh, ArrowDown } from '@element-plus/icons-vue'

export default defineComponent({
  name: 'SearchForm',
  components: {
    Search,
    Refresh,
    ArrowDown
  },
  props: {
    // 搜索字段配置
    searchFields: {
      type: Array,
      required: true,
      default: () => []
    },
    // 高级搜索字段配置
    advancedFields: {
      type: Array,
      default: () => []
    },
    // 是否显示高级搜索
    showAdvanced: {
      type: Boolean,
      default: false
    },
    // 加载状态
    loading: {
      type: Boolean,
      default: false
    },
    // 初始值
    initialValues: {
      type: Object,
      default: () => ({})
    }
  },
  emits: ['search', 'reset'],
  setup(props, { emit }) {
    const advancedVisible = ref(false)
    
    // 初始化搜索模型
    const initSearchModel = () => {
      const model = {}
      
      // 基础搜索字段
      props.searchFields.forEach(field => {
        model[field.key] = props.initialValues[field.key] || field.defaultValue || ''
      })
      
      // 高级搜索字段
      props.advancedFields.forEach(field => {
        model[field.key] = props.initialValues[field.key] || field.defaultValue || ''
      })
      
      return model
    }
    
    const searchModel = reactive(initSearchModel())
    
    // 监听初始值变化
    watch(() => props.initialValues, (newValues) => {
      Object.assign(searchModel, newValues)
    }, { deep: true })
    
    // 搜索处理
    const handleSearch = () => {
      // 过滤空值
      const searchData = {}
      Object.keys(searchModel).forEach(key => {
        const value = searchModel[key]
        if (value !== '' && value !== null && value !== undefined) {
          searchData[key] = value
        }
      })
      
      emit('search', searchData)
    }
    
    // 重置处理
    const handleReset = () => {
      // 重置为默认值
      const resetModel = initSearchModel()
      Object.assign(searchModel, resetModel)
      
      emit('reset', searchModel)
    }
    
    // 切换高级搜索
    const toggleAdvanced = () => {
      advancedVisible.value = !advancedVisible.value
    }
    
    return {
      searchModel,
      advancedVisible,
      handleSearch,
      handleReset,
      toggleAdvanced
    }
  }
})
</script>

<style scoped>
.search-form-container {
  background: var(--bg-primary);
  border: 1px solid var(--border-secondary);
  border-radius: var(--radius-lg);
  padding: var(--spacing-lg);
  margin-bottom: var(--spacing-lg);
  box-shadow: var(--shadow-sm);
}

.search-form {
  margin-bottom: 0;
}

.search-actions {
  margin-left: auto;
}

.advanced-toggle {
  margin-left: var(--spacing-sm);
}

.advanced-icon {
  margin-left: var(--spacing-xs);
  transition: var(--transition-normal);
}

.advanced-icon.is-expanded {
  transform: rotate(180deg);
}

.advanced-search {
  margin-top: var(--spacing-md);
  padding-top: var(--spacing-md);
  border-top: 1px solid var(--border-secondary);
}

:deep(.el-form-item) {
  margin-bottom: var(--spacing-md);
}

:deep(.el-form-item__label) {
  color: var(--text-secondary);
  font-weight: var(--font-weight-medium);
}
</style>

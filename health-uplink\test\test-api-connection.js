// 测试API连接的脚本
const axios = require('axios');

async function testLogin() {
  try {
    console.log('=== 测试登录API ===');
    const loginResponse = await axios.post('http://localhost:3000/api/user/login', {
      username: 'admin',
      password: 'admin123'
    });
    
    console.log('登录成功:', loginResponse.data);
    const token = loginResponse.data.token;
    
    if (token) {
      console.log('\n=== 测试图片列表API ===');
      const imageResponse = await axios.get('http://localhost:3000/api/image/list', {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });
      
      console.log('图片列表响应:', imageResponse.data);
    } else {
      console.error('未获取到token');
    }
    
  } catch (error) {
    console.error('测试失败:', error.response ? error.response.data : error.message);
  }
}

async function testHealth() {
  try {
    console.log('=== 测试健康检查API ===');
    const response = await axios.get('http://localhost:3000/health');
    console.log('健康检查响应:', response.data);
  } catch (error) {
    console.error('健康检查失败:', error.message);
  }
}

async function runTests() {
  await testHealth();
  console.log('\n' + '='.repeat(50) + '\n');
  await testLogin();
}

runTests();

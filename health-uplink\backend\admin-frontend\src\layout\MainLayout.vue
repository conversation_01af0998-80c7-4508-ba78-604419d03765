<template>
  <el-container style="height: 100vh;">
    <el-aside width="200px" style="background-color: #545c64;">
      <div class="aside-header">
        <h3 style="color: #fff; text-align: center; line-height: 60px; margin: 0;">管理系统</h3>
      </div>
      <el-menu
        default-active="2"
        class="el-menu-vertical-demo"
        @open="handleOpen"
        @close="handleClose"
        background-color="#545c64"
        text-color="#fff"
        active-text-color="#ffd04b"
        router
      >
        <el-menu-item index="/patients">
          <i class="el-icon-document"></i>
          <span>患者管理</span>
        </el-menu-item>
        <el-menu-item index="/image">
          <i class="el-icon-picture"></i>
          <span>图片管理</span>
        </el-menu-item>
        <el-menu-item index="/export">
          <i class="el-icon-download"></i>
          <span>数据导出</span>
        </el-menu-item>
        <el-menu-item index="/components">
          <i class="el-icon-setting"></i>
          <span>组件展示</span>
        </el-menu-item>
        <el-menu-item index="/status">
          <i class="el-icon-monitor"></i>
          <span>系统状态</span>
        </el-menu-item>
      </el-menu>
    </el-aside>

    <el-container>
      <el-header style="text-align: right; font-size: 12px; border-bottom: 1px solid #eee;">
        <el-dropdown trigger="hover">
          <span class="el-dropdown-link">
            {{ username }}<i class="el-icon-arrow-down el-icon--right"></i>
          </span>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item @click="logout">用户退出</el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </el-header>

      <el-main>
        <router-view></router-view> <!-- 业务页面将在这里渲染 -->
      </el-main>
    </el-container>
  </el-container>
</template>

<script>
import { defineComponent, ref } from 'vue';
import { useRouter } from 'vue-router';

export default defineComponent({
  components: {
  },
  setup() {
    const router = useRouter();
    const username = ref(localStorage.getItem('username') || '管理员'); // 从本地存储获取用户名
    console.log('MainLayout setup executed. Username:', username.value); // 添加日志

    const handleOpen = (key, keyPath) => {
      console.log(key, keyPath);
    };
    const handleClose = (key, keyPath) => {
      console.log(key, keyPath);
    };

    const logout = () => {
      localStorage.removeItem('token');
      localStorage.removeItem('username');
      router.push('/login');
    };

    return {
      username,
      handleOpen,
      handleClose,
      logout
    };
  }
});
</script>

<style scoped>
.el-header {
  background-color: #fff; /* 改为白色背景 */
  color: #333; /* 保持字体颜色 */
  line-height: 60px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, .12), 0 0 6px rgba(0, 0, 0, .04); /* 添加阴影 */
  display: flex; /* 使用flex布局 */
  align-items: center; /* 垂直居中 */
  justify-content: flex-end; /* 内容靠右对齐 */
  padding: 0 20px; /* 增加内边距 */
}

.el-aside {
  color: #333;
}

.aside-header {
  height: 60px; /* 与header高度一致 */
  background-color: #434a52; /* 比菜单背景色稍深 */
  display: flex;
  align-items: center;
  justify-content: center;
}

.el-main {
  background-color: #f0f2f5; /* 统一背景色 */
  padding: 20px; /* 增加内边距 */
  box-sizing: border-box; /* 确保内边距不增加总宽度 */
  display: flex; /* 使用flex布局 */
  flex-direction: column; /* 内容垂直排列 */
  min-width: 1000px; /* 设置最小宽度 */
  min-height: 700px; /* 设置最小高度 */
}
</style>

const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');
const { Doctor } = require('../models');

const fs = require('fs'); // 引入 fs 模块

const logger = require('../utils/logger'); // 引入 logger

async function authenticateUser(username, password) {
  try {
    fs.appendFileSync('debug.log', `尝试认证用户: username=${username}, password=${password}\n`); // 添加文件日志
    logger.info('尝试认证用户:', { username, password }); // 添加日志
    const doctor = await Doctor.findOne({ where: { name: username } });
    fs.appendFileSync('debug.log', `查询到的用户: ${JSON.stringify(doctor)}\n`); // 添加文件日志
    logger.info('查询到的用户:', doctor); // 添加日志
    if (!doctor) {
      fs.appendFileSync('debug.log', `认证失败: 用户名不存在: ${username}\n`); // 添加文件日志
      logger.info('认证失败: 用户名不存在', { username }); // 添加日志
      return { success: false, message: '用户名或密码不正确。' };
    }

    const isMatch = await bcrypt.compare(password, doctor.password);
    fs.appendFileSync('debug.log', `密码比对结果 (isMatch): ${isMatch}\n`); // 添加文件日志
    logger.info('密码比对结果 (isMatch):', isMatch); // 添加日志
    if (!isMatch) {
      fs.appendFileSync('debug.log', `认证失败: 密码不匹配: ${username}\n`); // 添加文件日志
      logger.info('认证失败: 密码不匹配', { username }); // 添加日志
      return { success: false, message: '用户名或密码不正确。' };
    }

    const token = jwt.sign(
      { id: doctor.doctor_id, name: doctor.name },
      process.env.JWT_SECRET || 'your_jwt_secret',
      { expiresIn: '1h' }
    );

    return { success: true, token, doctor: { id: doctor.doctor_id, name: doctor.name } };
  } catch (error) {
    logger.error('服务层：用户认证失败:', error);
    return { success: false, message: error.message };
  }
}

async function createDefaultAdmin() {
  try {
    // 首先检查admin用户是否已存在
    const existingAdmin = await Doctor.findOne({ where: { name: 'admin' } });
    if (existingAdmin) {
      logger.info('默认管理员账户已存在，跳过创建');
      return { success: true, message: '管理员账户已存在', doctor: existingAdmin };
    }

    // 如果不存在，则创建新的admin账户
    const hashedPassword = await bcrypt.hash('admin123', 10); // 默认密码 'admin123'
    const adminDoctor = await Doctor.create({
      name: 'admin',
      password: hashedPassword
    });
    logger.info('默认管理员账户创建成功:', adminDoctor.name);

    // 验证创建结果
    const verifiedAdmin = await Doctor.findOne({ where: { name: 'admin' } });
    logger.info('验证默认管理员用户是否存在:', verifiedAdmin ? '存在' : '不存在');
    if (verifiedAdmin) {
      logger.info('验证到的管理员用户数据:', { id: verifiedAdmin.doctor_id, name: verifiedAdmin.name }); // 不打印密码
    }

    return { success: true, doctor: adminDoctor };
  } catch (error) {
    logger.error('服务层：创建默认管理员账户失败:', error);
    return { success: false, message: error.message };
  }
}

module.exports = {
  authenticateUser,
  createDefaultAdmin
};
# 公共组件使用指南

本项目提供了一套标准化的公共组件，已注册为全局组件，可在任何页面中直接使用。

## 🚀 快速开始

所有公共组件已在 `main.js` 中注册为全局组件，无需在页面中单独导入。

## 📦 组件列表

### 1. ContentContainer - 页面内容容器

标准化的页面布局容器，提供统一的页面结构。

```vue
<template>
  <ContentContainer
    title="页面标题"
    description="页面描述信息"
    :show-header="true"
    :breadcrumbs="breadcrumbs"
    :show-breadcrumb="true"
  >
    <!-- 页面内容 -->
    <div>页面内容区域</div>
  </ContentContainer>
</template>

<script>
export default {
  setup() {
    const breadcrumbs = computed(() => [
      { title: '首页', path: '/' },
      { title: '当前页面', path: '/current' }
    ]);
    
    return { breadcrumbs };
  }
}
</script>
```

### 2. DataTable - 数据表格

功能完整的数据表格组件，内置分页、搜索、排序等功能。

```vue
<template>
  <DataTable
    :data="tableData"
    :columns="columns"
    :loading="loading"
    :pagination="pagination"
    title="数据列表"
    @current-change="handlePageChange"
    @size-change="handleSizeChange"
    @refresh="fetchData"
  >
    <!-- 自定义列插槽 -->
    <template #status="{ row }">
      <el-tag :type="getStatusType(row.status)">
        {{ getStatusText(row.status) }}
      </el-tag>
    </template>
    
    <!-- 自定义操作列 -->
    <template #actions="{ row }">
      <el-button type="text" @click="editRow(row)">编辑</el-button>
      <el-button type="text" @click="deleteRow(row)">删除</el-button>
    </template>
  </DataTable>
</template>

<script>
export default {
  setup() {
    const tableData = ref([]);
    const loading = ref(false);
    const pagination = ref({
      page: 1,
      pageSize: 10,
      total: 0
    });
    
    const columns = computed(() => [
      { prop: 'id', label: 'ID', width: 80 },
      { prop: 'name', label: '名称', minWidth: 150 },
      { prop: 'status', label: '状态', width: 100, slot: 'status' },
      { prop: 'actions', label: '操作', width: 150, slot: 'actions' }
    ]);
    
    return {
      tableData,
      loading,
      pagination,
      columns
    };
  }
}
</script>
```

### 3. SearchForm - 搜索表单

标准化的搜索表单组件，支持多种输入类型。

```vue
<template>
  <SearchForm
    :search-fields="searchFields"
    :loading="loading"
    @search="handleSearch"
    @reset="handleReset"
  />
</template>

<script>
export default {
  setup() {
    const searchFields = computed(() => [
      {
        key: 'name',
        label: '名称',
        type: 'input',
        placeholder: '请输入名称',
        clearable: true
      },
      {
        key: 'status',
        label: '状态',
        type: 'select',
        options: [
          { label: '启用', value: 1 },
          { label: '禁用', value: 0 }
        ]
      },
      {
        key: 'date_range',
        label: '创建时间',
        type: 'daterange',
        placeholder: '选择时间范围'
      }
    ]);
    
    const handleSearch = (searchData) => {
      console.log('搜索参数:', searchData);
      // 执行搜索逻辑
    };
    
    const handleReset = () => {
      console.log('重置搜索');
      // 执行重置逻辑
    };
    
    return {
      searchFields,
      handleSearch,
      handleReset
    };
  }
}
</script>
```

### 4. StandardPagination - 标准分页

统一的分页组件，提供一致的分页体验。

```vue
<template>
  <StandardPagination
    :current-page="currentPage"
    :page-size="pageSize"
    :total="total"
    :page-sizes="[10, 20, 50, 100]"
    :show-info="true"
    @current-change="handlePageChange"
    @size-change="handleSizeChange"
  />
</template>

<script>
export default {
  setup() {
    const currentPage = ref(1);
    const pageSize = ref(10);
    const total = ref(0);
    
    const handlePageChange = (page) => {
      currentPage.value = page;
      // 加载数据
    };
    
    const handleSizeChange = (size) => {
      pageSize.value = size;
      currentPage.value = 1;
      // 加载数据
    };
    
    return {
      currentPage,
      pageSize,
      total,
      handlePageChange,
      handleSizeChange
    };
  }
}
</script>
```

### 5. ImageManager - 图片管理组件

功能完整的图片管理组件，支持网格/列表视图切换、上传、预览、删除等功能。

```vue
<template>
  <ImageManager
    :images="images"
    :loading="loading"
    :pagination="pagination"
    :allow-upload="true"
    :allow-delete="true"
    :allow-download="true"
    :show-patient-info="true"
    :show-debug-info="false"
    empty-description="暂无图片"
    empty-action-text="上传图片"
    table-title="图片列表"
    @upload="handleUpload"
    @delete="handleDelete"
    @download="handleDownload"
    @current-change="handlePageChange"
    @size-change="handleSizeChange"
    @refresh="fetchImages"
  >
    <!-- 自定义工具栏操作 -->
    <template #toolbar-actions>
      <el-button type="primary" @click="handleUpload">上传图片</el-button>
      <el-button type="info" @click="handleBatchOperation">批量操作</el-button>
    </template>
  </ImageManager>
</template>

<script>
export default {
  setup() {
    const images = ref([]);
    const loading = ref(false);
    const pagination = ref({
      page: 1,
      pageSize: 12,
      total: 0
    });
    
    const handleUpload = () => {
      console.log('上传图片');
    };
    
    const handleDelete = async (image) => {
      console.log('删除图片:', image);
      // 执行删除逻辑
    };
    
    const handleDownload = (image) => {
      console.log('下载图片:', image);
    };
    
    const fetchImages = async () => {
      loading.value = true;
      try {
        // 获取图片数据
        // images.value = response.data;
        // pagination.value.total = response.total;
      } finally {
        loading.value = false;
      }
    };
    
    return {
      images,
      loading,
      pagination,
      handleUpload,
      handleDelete,
      handleDownload,
      fetchImages
    };
  }
}
</script>
```

## 🎨 样式系统

所有组件都使用统一的设计系统变量，确保视觉一致性：

- 颜色：`var(--primary-color)`, `var(--success-color)` 等
- 间距：`var(--spacing-xs)`, `var(--spacing-sm)` 等
- 圆角：`var(--radius-sm)`, `var(--radius-md)` 等
- 阴影：`var(--shadow-sm)`, `var(--shadow-md)` 等

## 📝 最佳实践

1. **统一使用公共组件**：优先使用公共组件，避免重复开发
2. **保持接口一致**：组件事件和属性命名保持一致
3. **响应式设计**：所有组件都支持响应式布局
4. **可访问性**：组件遵循无障碍设计原则
5. **性能优化**：合理使用组件的懒加载和虚拟滚动功能

## 🔧 扩展开发

如需扩展组件功能，请：

1. 在对应组件文件中添加新的 props 和事件
2. 更新组件文档和使用示例
3. 确保向后兼容性
4. 添加相应的测试用例

## 📞 技术支持

如有问题或建议，请联系开发团队或提交 Issue。
